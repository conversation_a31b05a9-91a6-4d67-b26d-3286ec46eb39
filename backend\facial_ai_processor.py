"""
Facial AI Processor for Podcast Video Enhancement
Implements real-time face detection, voice activity detection, and dynamic zoom functionality
"""

import cv2
import numpy as np
import mediapipe as mp
import librosa
import soundfile as sf
from pydub import AudioSegment
from pydub.silence import detect_nonsilent
import ffmpeg
import os
import json
import logging
from typing import List, Dict, Tuple, Optional, Any
from dataclasses import dataclass
from datetime import datetime
import asyncio
import tempfile
import requests
from pathlib import Path

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class FaceDetection:
    """Data class for face detection results"""
    timestamp: float
    bbox: Tuple[int, int, int, int]  # x, y, width, height
    confidence: float
    landmarks: Optional[List[Tuple[int, int]]] = None
    speaker_id: Optional[int] = None

@dataclass
class VoiceActivity:
    """Data class for voice activity detection results"""
    start_time: float
    end_time: float
    confidence: float
    speaker_id: Optional[int] = None

@dataclass
class ZoomConfig:
    """Configuration for zoom effects"""
    zoom_level: float = 1.5  # 1.0 = no zoom, 2.0 = 2x zoom
    transition_speed: float = 0.5  # seconds for zoom transition
    detection_sensitivity: float = 0.7  # confidence threshold for face detection
    voice_threshold: float = 0.3  # threshold for voice activity detection
    padding: int = 50  # padding around face in pixels

class FacialAIProcessor:
    """Main class for facial AI processing of podcast videos"""
    
    def __init__(self, faces_api_key: str = None):
        """Initialize the facial AI processor"""
        self.faces_api_key = faces_api_key or "eyJraWQiOm51bGwsImFsZyI6IlJTMjU2In0"
        self.faces_db_url = "https://faces.mpdl.mpg.de/imeji/user?email=ivaturi.anish%40gmail.com"
        
        # Initialize MediaPipe Face Detection
        self.mp_face_detection = mp.solutions.face_detection
        self.mp_drawing = mp.solutions.drawing_utils
        self.face_detection = self.mp_face_detection.FaceDetection(
            model_selection=1,  # 0 for short-range, 1 for full-range
            min_detection_confidence=0.5
        )
        
        # Initialize MediaPipe Face Mesh for landmarks
        self.mp_face_mesh = mp.solutions.face_mesh
        self.face_mesh = self.mp_face_mesh.FaceMesh(
            static_image_mode=False,
            max_num_faces=5,
            refine_landmarks=True,
            min_detection_confidence=0.5,
            min_tracking_confidence=0.5
        )
        
        logger.info("Facial AI Processor initialized successfully")

    def analyze_video(self, video_path: str, config: ZoomConfig = None) -> Dict[str, Any]:
        """
        Analyze video for faces and speaking segments
        
        Args:
            video_path: Path to the input video file
            config: Zoom configuration parameters
            
        Returns:
            Dictionary containing analysis results
        """
        if config is None:
            config = ZoomConfig()
            
        logger.info(f"Starting video analysis for: {video_path}")
        
        try:
            # Extract audio for voice activity detection
            audio_path = self._extract_audio(video_path)

            # Detect voice activity
            voice_activities = self._detect_voice_activity(audio_path, config)

            # Detect faces in video
            face_detections = self._detect_faces_in_video(video_path, config)
            
            # Correlate voice activity with face detections
            speaker_segments = self._correlate_voice_and_faces(voice_activities, face_detections)
            
            # Generate zoom timeline
            zoom_timeline = self._generate_zoom_timeline(speaker_segments, config)
            
            # Clean up temporary audio file
            if os.path.exists(audio_path):
                os.remove(audio_path)
            
            analysis_result = {
                "video_path": video_path,
                "duration": self._get_video_duration(video_path),
                "face_detections": len(face_detections),
                "voice_segments": len(voice_activities),
                "speaker_segments": speaker_segments,
                "zoom_timeline": zoom_timeline,
                "config": config.__dict__,
                "timestamp": datetime.now().isoformat()
            }
            
            logger.info(f"Video analysis completed. Found {len(face_detections)} face detections and {len(voice_activities)} voice segments")
            return analysis_result
            
        except Exception as e:
            logger.error(f"Error during video analysis: {str(e)}")
            raise

    def process_video_with_face_zoom(self, video_path: str, output_path: str, config: ZoomConfig = None) -> str:
        """
        Process video with dynamic face zoom effects
        
        Args:
            video_path: Path to input video
            output_path: Path for output video
            config: Zoom configuration
            
        Returns:
            Path to processed video
        """
        if config is None:
            config = ZoomConfig()
            
        logger.info(f"Processing video with face zoom: {video_path}")
        
        try:
            # First analyze the video
            analysis = self.analyze_video(video_path, config)

            # Apply zoom effects based on analysis
            processed_path = self._apply_zoom_effects(
                video_path, 
                output_path, 
                analysis["zoom_timeline"], 
                config
            )
            
            logger.info(f"Video processing completed: {processed_path}")
            return processed_path
            
        except Exception as e:
            logger.error(f"Error during video processing: {str(e)}")
            raise

    def generate_preview(self, video_path: str, config: ZoomConfig = None) -> Dict[str, Any]:
        """
        Generate preview with face tracking overlay
        
        Args:
            video_path: Path to input video
            config: Zoom configuration
            
        Returns:
            Preview data with face tracking information
        """
        if config is None:
            config = ZoomConfig()
            
        logger.info(f"Generating preview for: {video_path}")
        
        try:
            # Analyze video
            analysis = self.analyze_video(video_path, config)

            # Generate preview frames with face overlays
            preview_frames = self._generate_preview_frames(video_path, analysis, config)
            
            preview_data = {
                "video_path": video_path,
                "analysis": analysis,
                "preview_frames": preview_frames,
                "total_frames": len(preview_frames),
                "timestamp": datetime.now().isoformat()
            }
            
            logger.info(f"Preview generated with {len(preview_frames)} frames")
            return preview_data
            
        except Exception as e:
            logger.error(f"Error generating preview: {str(e)}")
            raise

    def _extract_audio(self, video_path: str) -> str:
        """Extract audio from video for voice activity detection"""
        temp_audio = tempfile.mktemp(suffix=".wav")

        try:
            # Check if video has audio streams
            probe = ffmpeg.probe(video_path)
            audio_streams = [stream for stream in probe['streams'] if stream['codec_type'] == 'audio']

            if not audio_streams:
                logger.warning(f"No audio streams found in {video_path}, creating silent audio")
                # Create a silent audio file for videos without audio
                duration = float(probe['format']['duration'])
                (
                    ffmpeg
                    .input('anullsrc=channel_layout=mono:sample_rate=16000', f='lavfi', t=duration)
                    .output(temp_audio, acodec='pcm_s16le')
                    .overwrite_output()
                    .run(capture_stdout=True, capture_stderr=True)
                )
            else:
                # Extract audio normally
                (
                    ffmpeg
                    .input(video_path)
                    .output(temp_audio, acodec='pcm_s16le', ac=1, ar='16000')
                    .overwrite_output()
                    .run(capture_stdout=True, capture_stderr=True)
                )

            return temp_audio
        except ffmpeg.Error as e:
            logger.error(f"Error extracting audio: {e.stderr.decode()}")
            raise
        except Exception as e:
            logger.error(f"Error during audio extraction: {str(e)}")
            raise

    def _detect_voice_activity(self, audio_path: str, config: ZoomConfig) -> List[VoiceActivity]:
        """Detect voice activity in audio using multiple methods"""
        voice_activities = []
        
        try:
            # Load audio
            audio = AudioSegment.from_wav(audio_path)
            
            # Method 1: Silence detection using pydub
            nonsilent_ranges = detect_nonsilent(
                audio,
                min_silence_len=500,  # 500ms minimum silence
                silence_thresh=audio.dBFS - 16  # 16dB below average
            )
            
            for start_ms, end_ms in nonsilent_ranges:
                voice_activities.append(VoiceActivity(
                    start_time=start_ms / 1000.0,
                    end_time=end_ms / 1000.0,
                    confidence=0.8  # Default confidence for silence detection
                ))
            
            # Method 2: Energy-based VAD using librosa
            y, sr = librosa.load(audio_path, sr=16000)
            
            # Compute short-time energy
            frame_length = int(0.025 * sr)  # 25ms frames
            hop_length = int(0.010 * sr)   # 10ms hop
            
            energy = librosa.feature.rms(
                y=y, 
                frame_length=frame_length, 
                hop_length=hop_length
            )[0]
            
            # Threshold-based VAD
            energy_threshold = np.mean(energy) * config.voice_threshold
            voice_frames = energy > energy_threshold
            
            # Convert frame-based detection to time segments
            times = librosa.frames_to_time(
                np.arange(len(voice_frames)), 
                sr=sr, 
                hop_length=hop_length
            )
            
            # Group consecutive voice frames
            in_speech = False
            start_time = 0
            
            for i, (time, is_voice) in enumerate(zip(times, voice_frames)):
                if is_voice and not in_speech:
                    start_time = time
                    in_speech = True
                elif not is_voice and in_speech:
                    voice_activities.append(VoiceActivity(
                        start_time=start_time,
                        end_time=time,
                        confidence=0.9
                    ))
                    in_speech = False
            
            # Handle case where speech continues to end
            if in_speech:
                voice_activities.append(VoiceActivity(
                    start_time=start_time,
                    end_time=times[-1],
                    confidence=0.9
                ))
            
            # Merge overlapping segments and sort
            voice_activities = self._merge_voice_segments(voice_activities)
            
            logger.info(f"Detected {len(voice_activities)} voice activity segments")
            return voice_activities
            
        except Exception as e:
            logger.error(f"Error in voice activity detection: {str(e)}")
            return []

    def _detect_faces_in_video(self, video_path: str, config: ZoomConfig) -> List[FaceDetection]:
        """Detect faces in video frames"""
        face_detections = []
        
        try:
            cap = cv2.VideoCapture(video_path)
            fps = cap.get(cv2.CAP_PROP_FPS)
            frame_count = 0
            
            while cap.isOpened():
                ret, frame = cap.read()
                if not ret:
                    break
                
                timestamp = frame_count / fps
                
                # Convert BGR to RGB for MediaPipe
                rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                
                # Detect faces
                results = self.face_detection.process(rgb_frame)
                
                if results.detections:
                    for detection in results.detections:
                        # Extract bounding box
                        bbox = detection.location_data.relative_bounding_box
                        h, w, _ = frame.shape
                        
                        x = int(bbox.xmin * w)
                        y = int(bbox.ymin * h)
                        width = int(bbox.width * w)
                        height = int(bbox.height * h)
                        
                        confidence = detection.score[0]
                        
                        if confidence >= config.detection_sensitivity:
                            face_detections.append(FaceDetection(
                                timestamp=timestamp,
                                bbox=(x, y, width, height),
                                confidence=confidence
                            ))
                
                frame_count += 1
                
                # Process every 5th frame for performance
                for _ in range(4):
                    ret, _ = cap.read()
                    if not ret:
                        break
                    frame_count += 1
            
            cap.release()
            
            logger.info(f"Detected {len(face_detections)} faces in video")
            return face_detections
            
        except Exception as e:
            logger.error(f"Error in face detection: {str(e)}")
            return []

    def _correlate_voice_and_faces(self, voice_activities: List[VoiceActivity], face_detections: List[FaceDetection]) -> List[Dict[str, Any]]:
        """Correlate voice activity with face detections to identify speakers"""
        speaker_segments = []
        
        for voice_segment in voice_activities:
            # Find faces that appear during this voice segment
            concurrent_faces = []
            
            for face in face_detections:
                if voice_segment.start_time <= face.timestamp <= voice_segment.end_time:
                    concurrent_faces.append(face)
            
            if concurrent_faces:
                # Group faces by proximity (same speaker)
                speaker_faces = self._group_faces_by_speaker(concurrent_faces)
                
                for speaker_id, faces in speaker_faces.items():
                    # Calculate average position and confidence
                    avg_bbox = self._calculate_average_bbox(faces)
                    avg_confidence = np.mean([f.confidence for f in faces])
                    
                    speaker_segments.append({
                        "start_time": voice_segment.start_time,
                        "end_time": voice_segment.end_time,
                        "speaker_id": speaker_id,
                        "face_bbox": avg_bbox,
                        "confidence": avg_confidence,
                        "face_count": len(faces)
                    })
        
        return speaker_segments

    def _generate_zoom_timeline(self, speaker_segments: List[Dict[str, Any]], config: ZoomConfig) -> List[Dict[str, Any]]:
        """Generate timeline for zoom effects"""
        zoom_timeline = []

        for segment in speaker_segments:
            # Calculate face center from bbox
            bbox = segment["face_bbox"]
            face_center_x = bbox[0] + bbox[2] // 2  # x + width/2
            face_center_y = bbox[1] + bbox[3] // 2  # y + height/2

            zoom_timeline.append({
                "start_time": segment["start_time"],
                "end_time": segment["end_time"],
                "zoom_level": config.zoom_level,
                "target_bbox": segment["face_bbox"],
                "face_center": (face_center_x, face_center_y),
                "transition_speed": config.transition_speed,
                "speaker_id": segment["speaker_id"]
            })

        return zoom_timeline

    def _merge_voice_segments(self, segments: List[VoiceActivity]) -> List[VoiceActivity]:
        """Merge overlapping voice activity segments"""
        if not segments:
            return []
        
        # Sort by start time
        segments.sort(key=lambda x: x.start_time)
        
        merged = [segments[0]]
        
        for current in segments[1:]:
            last = merged[-1]
            
            # If segments overlap or are very close (within 0.5 seconds)
            if current.start_time <= last.end_time + 0.5:
                # Merge segments
                merged[-1] = VoiceActivity(
                    start_time=last.start_time,
                    end_time=max(last.end_time, current.end_time),
                    confidence=max(last.confidence, current.confidence)
                )
            else:
                merged.append(current)
        
        return merged

    def _group_faces_by_speaker(self, faces: List[FaceDetection]) -> Dict[int, List[FaceDetection]]:
        """Group faces by speaker using spatial clustering"""
        if not faces:
            return {}
        
        # Simple clustering based on face position
        speakers = {}
        speaker_id = 0
        
        for face in faces:
            assigned = False
            
            # Check if face belongs to existing speaker
            for sid, speaker_faces in speakers.items():
                if self._faces_belong_to_same_speaker(face, speaker_faces):
                    speakers[sid].append(face)
                    assigned = True
                    break
            
            # Create new speaker if not assigned
            if not assigned:
                speakers[speaker_id] = [face]
                speaker_id += 1
        
        return speakers

    def _faces_belong_to_same_speaker(self, face: FaceDetection, speaker_faces: List[FaceDetection]) -> bool:
        """Check if a face belongs to the same speaker based on position similarity"""
        if not speaker_faces:
            return False
        
        # Calculate average position of existing speaker faces
        avg_x = np.mean([f.bbox[0] + f.bbox[2]/2 for f in speaker_faces])
        avg_y = np.mean([f.bbox[1] + f.bbox[3]/2 for f in speaker_faces])
        
        # Calculate face center
        face_x = face.bbox[0] + face.bbox[2]/2
        face_y = face.bbox[1] + face.bbox[3]/2
        
        # Check if within reasonable distance (adjust threshold as needed)
        distance = np.sqrt((face_x - avg_x)**2 + (face_y - avg_y)**2)
        threshold = 100  # pixels
        
        return distance < threshold

    def _calculate_average_bbox(self, faces: List[FaceDetection]) -> Tuple[int, int, int, int]:
        """Calculate average bounding box from multiple face detections"""
        if not faces:
            return (0, 0, 0, 0)
        
        avg_x = int(np.mean([f.bbox[0] for f in faces]))
        avg_y = int(np.mean([f.bbox[1] for f in faces]))
        avg_w = int(np.mean([f.bbox[2] for f in faces]))
        avg_h = int(np.mean([f.bbox[3] for f in faces]))
        
        return (avg_x, avg_y, avg_w, avg_h)

    def _get_video_duration(self, video_path: str) -> float:
        """Get video duration in seconds"""
        try:
            probe = ffmpeg.probe(video_path)
            duration = float(probe['streams'][0]['duration'])
            return duration
        except:
            return 0.0

    def _apply_zoom_effects(self, input_path: str, output_path: str, zoom_timeline: List[Dict[str, Any]], config: ZoomConfig) -> str:
        """Apply zoom effects to video based on timeline"""
        logger.info(f"Applying zoom effects with {len(zoom_timeline)} segments")

        if not zoom_timeline:
            logger.info("No zoom segments to apply, copying original video")
            import shutil
            shutil.copy2(input_path, output_path)
            return output_path

        try:
            # Open input video
            cap = cv2.VideoCapture(input_path)
            if not cap.isOpened():
                raise ValueError(f"Could not open video: {input_path}")

            # Get video properties
            fps = cap.get(cv2.CAP_PROP_FPS)
            width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))

            logger.info(f"Processing video: {width}x{height} @ {fps}fps, {total_frames} frames")

            # Setup video writer
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            out = cv2.VideoWriter(output_path, fourcc, fps, (width, height))

            if not out.isOpened():
                raise ValueError(f"Could not create output video: {output_path}")

            # Create zoom timeline lookup for efficient processing
            zoom_lookup = {}
            for segment in zoom_timeline:
                start_frame = int(segment['start_time'] * fps)
                end_frame = int(segment['end_time'] * fps)
                zoom_level = segment['zoom_level']
                face_center = segment.get('face_center', (width // 2, height // 2))

                for frame_idx in range(start_frame, min(end_frame + 1, total_frames)):
                    zoom_lookup[frame_idx] = {
                        'zoom_level': zoom_level,
                        'face_center': face_center
                    }

            logger.info(f"Created zoom lookup for {len(zoom_lookup)} frames")

            # Process each frame
            frame_count = 0
            processed_frames = 0

            while cap.isOpened():
                ret, frame = cap.read()
                if not ret:
                    break

                # Check if this frame needs zoom effect
                if frame_count in zoom_lookup:
                    zoom_info = zoom_lookup[frame_count]
                    frame = self._apply_zoom_to_frame(
                        frame,
                        zoom_info['zoom_level'],
                        zoom_info['face_center'],
                        config
                    )
                    processed_frames += 1

                # Write frame to output
                out.write(frame)
                frame_count += 1

                # Progress logging
                if frame_count % 1000 == 0:
                    progress = (frame_count / total_frames) * 100
                    logger.info(f"Processing progress: {progress:.1f}% ({frame_count}/{total_frames})")

            # Cleanup
            cap.release()
            out.release()

            logger.info(f"Zoom effects applied successfully!")
            logger.info(f"  - Total frames processed: {frame_count}")
            logger.info(f"  - Frames with zoom effects: {processed_frames}")
            logger.info(f"  - Output video: {output_path}")

            return output_path

        except Exception as e:
            logger.error(f"Error applying zoom effects: {e}")
            # Fallback: copy original video
            import shutil
            shutil.copy2(input_path, output_path)
            return output_path

    def _apply_zoom_to_frame(self, frame: np.ndarray, zoom_level: float, face_center: tuple, config: ZoomConfig) -> np.ndarray:
        """Apply zoom effect to a single frame centered on face"""
        try:
            if zoom_level <= 1.0:
                return frame  # No zoom needed

            height, width = frame.shape[:2]
            face_x, face_y = face_center

            # Ensure face center is within frame bounds
            face_x = max(0, min(width - 1, int(face_x)))
            face_y = max(0, min(height - 1, int(face_y)))

            # Calculate crop dimensions for zoom
            crop_width = int(width / zoom_level)
            crop_height = int(height / zoom_level)

            # Calculate crop region centered on face
            crop_x1 = max(0, face_x - crop_width // 2)
            crop_y1 = max(0, face_y - crop_height // 2)
            crop_x2 = min(width, crop_x1 + crop_width)
            crop_y2 = min(height, crop_y1 + crop_height)

            # Adjust crop region if it goes out of bounds
            if crop_x2 - crop_x1 < crop_width:
                crop_x1 = max(0, crop_x2 - crop_width)
            if crop_y2 - crop_y1 < crop_height:
                crop_y1 = max(0, crop_y2 - crop_height)

            # Crop the frame
            cropped_frame = frame[crop_y1:crop_y2, crop_x1:crop_x2]

            # Resize cropped frame back to original dimensions (this creates the zoom effect)
            zoomed_frame = cv2.resize(cropped_frame, (width, height), interpolation=cv2.INTER_LANCZOS4)

            return zoomed_frame

        except Exception as e:
            logger.warning(f"Error applying zoom to frame: {e}")
            return frame  # Return original frame on error

    def _generate_preview_frames(self, video_path: str, analysis: Dict[str, Any], config: ZoomConfig) -> List[Dict[str, Any]]:
        """Generate preview frames with face tracking overlays"""
        preview_frames = []
        
        try:
            cap = cv2.VideoCapture(video_path)
            fps = cap.get(cv2.CAP_PROP_FPS)
            frame_count = 0
            
            # Generate preview for first 30 seconds or 10 frames, whichever is less
            max_frames = min(int(30 * fps), 300)
            frame_interval = max(1, max_frames // 10)  # Sample 10 frames max
            
            while cap.isOpened() and frame_count < max_frames:
                ret, frame = cap.read()
                if not ret:
                    break
                
                if frame_count % frame_interval == 0:
                    timestamp = frame_count / fps
                    
                    # Find faces at this timestamp
                    current_faces = []
                    for segment in analysis.get("speaker_segments", []):
                        if segment["start_time"] <= timestamp <= segment["end_time"]:
                            current_faces.append(segment)
                    
                    preview_frames.append({
                        "timestamp": timestamp,
                        "frame_number": frame_count,
                        "faces": current_faces,
                        "has_speech": len(current_faces) > 0
                    })
                
                frame_count += 1
            
            cap.release()
            
        except Exception as e:
            logger.error(f"Error generating preview frames: {str(e)}")
        
        return preview_frames
