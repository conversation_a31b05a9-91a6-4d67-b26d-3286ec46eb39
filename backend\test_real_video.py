"""
Comprehensive test script for facial AI zoom functionality with real videos
"""

import os
import time
import json
import cv2
import numpy as np
import logging
from pathlib import Path

# Set up detailed logging
logging.basicConfig(
    level=logging.INFO, 
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('facial_ai_test.log')
    ]
)
logger = logging.getLogger(__name__)

def get_video_info(video_path):
    """Get detailed information about the video"""
    cap = cv2.VideoCapture(video_path)
    
    if not cap.isOpened():
        return None
    
    info = {
        'path': video_path,
        'file_size': os.path.getsize(video_path),
        'frame_count': int(cap.get(cv2.CAP_PROP_FRAME_COUNT)),
        'fps': cap.get(cv2.CAP_PROP_FPS),
        'width': int(cap.get(cv2.CAP_PROP_FRAME_WIDTH)),
        'height': int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT)),
        'duration': 0
    }
    
    if info['fps'] > 0:
        info['duration'] = info['frame_count'] / info['fps']
    
    cap.release()
    return info

def analyze_frame_differences(original_path, processed_path, sample_frames=10):
    """Analyze differences between original and processed video frames"""
    orig_cap = cv2.VideoCapture(original_path)
    proc_cap = cv2.VideoCapture(processed_path)
    
    if not orig_cap.isOpened() or not proc_cap.isOpened():
        logger.error("Could not open video files for comparison")
        return None
    
    orig_frames = int(orig_cap.get(cv2.CAP_PROP_FRAME_COUNT))
    proc_frames = int(proc_cap.get(cv2.CAP_PROP_FRAME_COUNT))
    
    # Sample frames evenly throughout the video
    frame_indices = np.linspace(0, min(orig_frames, proc_frames) - 1, sample_frames, dtype=int)
    
    differences = []
    
    for frame_idx in frame_indices:
        orig_cap.set(cv2.CAP_PROP_POS_FRAMES, frame_idx)
        proc_cap.set(cv2.CAP_PROP_POS_FRAMES, frame_idx)
        
        ret1, orig_frame = orig_cap.read()
        ret2, proc_frame = proc_cap.read()
        
        if ret1 and ret2:
            # Calculate various difference metrics
            diff = cv2.absdiff(orig_frame, proc_frame)
            mean_diff = np.mean(diff)
            max_diff = np.max(diff)
            
            # Calculate structural similarity
            orig_gray = cv2.cvtColor(orig_frame, cv2.COLOR_BGR2GRAY)
            proc_gray = cv2.cvtColor(proc_frame, cv2.COLOR_BGR2GRAY)
            
            differences.append({
                'frame_index': frame_idx,
                'timestamp': frame_idx / orig_cap.get(cv2.CAP_PROP_FPS),
                'mean_difference': mean_diff,
                'max_difference': max_diff,
                'has_significant_change': mean_diff > 10  # Threshold for zoom detection
            })
    
    orig_cap.release()
    proc_cap.release()
    
    return differences

def test_facial_ai_with_real_video():
    """Test facial AI functionality with real video from videos folder"""
    logger.info("🎬 Starting Real Video Facial AI Test")
    logger.info("=" * 60)
    
    # Find videos in the videos folder
    videos_dir = Path("videos")
    if not videos_dir.exists():
        logger.error("❌ Videos folder not found!")
        return False
    
    video_files = list(videos_dir.glob("*.mp4")) + list(videos_dir.glob("*.avi")) + list(videos_dir.glob("*.mov"))
    
    if not video_files:
        logger.error("❌ No video files found in videos folder!")
        return False
    
    # Use the first video file
    input_video = str(video_files[0])
    logger.info(f"📹 Testing with video: {input_video}")
    
    # Get video information
    video_info = get_video_info(input_video)
    if not video_info:
        logger.error("❌ Could not read video information")
        return False
    
    logger.info("📊 Video Information:")
    logger.info(f"  - File size: {video_info['file_size'] / (1024*1024):.1f} MB")
    logger.info(f"  - Duration: {video_info['duration']:.1f} seconds")
    logger.info(f"  - Resolution: {video_info['width']}x{video_info['height']}")
    logger.info(f"  - FPS: {video_info['fps']:.1f}")
    logger.info(f"  - Frame count: {video_info['frame_count']}")
    
    try:
        # Import facial AI processor
        from facial_ai_processor import FacialAIProcessor, ZoomConfig
        
        # Create processor and configuration
        processor = FacialAIProcessor()
        config = ZoomConfig(
            zoom_level=2.0,  # 2x zoom for more visible effect
            detection_sensitivity=0.6,
            voice_threshold=0.3,
            transition_speed=0.5
        )
        
        logger.info("✅ Facial AI Processor initialized")
        logger.info(f"📋 Configuration: zoom={config.zoom_level}x, sensitivity={config.detection_sensitivity}")
        
        # Step 1: Analyze the video
        logger.info("\n🔍 Step 1: Analyzing video...")
        start_time = time.time()
        
        analysis_result = processor.analyze_video(input_video, config)
        analysis_time = time.time() - start_time
        
        logger.info(f"✅ Analysis completed in {analysis_time:.1f} seconds")
        logger.info("📈 Analysis Results:")
        logger.info(f"  - Face detections: {analysis_result.get('face_detections', 0)}")
        logger.info(f"  - Voice segments: {analysis_result.get('voice_segments', 0)}")
        logger.info(f"  - Speaker segments: {len(analysis_result.get('speaker_segments', []))}")
        logger.info(f"  - Zoom timeline entries: {len(analysis_result.get('zoom_timeline', []))}")

        # Get actual counts for later use
        face_detection_count = analysis_result.get('face_detections', 0)
        voice_segment_count = analysis_result.get('voice_segments', 0)
        speaker_segments = analysis_result.get('speaker_segments', [])
        zoom_timeline = analysis_result.get('zoom_timeline', [])

        # Show speaker segments (which contain face detection info)
        if speaker_segments:
            logger.info("👤 Speaker Segments with Face Info:")
            for i, segment in enumerate(speaker_segments[:5]):
                start = segment.get('start_time', 0)
                end = segment.get('end_time', 0)
                confidence = segment.get('confidence', 0)
                logger.info(f"  - Segment {i+1}: {start:.2f}s - {end:.2f}s (confidence: {confidence:.2f})")

        # Show zoom timeline
        if zoom_timeline:
            logger.info("🎯 Zoom Timeline:")
            for i, entry in enumerate(zoom_timeline[:5]):
                start = entry.get('start_time', 0)
                end = entry.get('end_time', 0)
                zoom = entry.get('zoom_level', 1.0)
                logger.info(f"  - Segment {i+1}: {start:.2f}s - {end:.2f}s (zoom: {zoom:.1f}x)")
        
        # Step 2: Process video with zoom effects
        logger.info("\n🎬 Step 2: Processing video with zoom effects...")
        output_video = f"videos/processed_{Path(input_video).stem}_zoom.mp4"
        
        start_time = time.time()
        result_path = processor.process_video_with_face_zoom(input_video, output_video, config)
        processing_time = time.time() - start_time
        
        logger.info(f"✅ Video processing completed in {processing_time:.1f} seconds")
        logger.info(f"📁 Output video: {result_path}")
        
        # Step 3: Verify the output
        if os.path.exists(result_path):
            output_info = get_video_info(result_path)
            logger.info("📊 Output Video Information:")
            logger.info(f"  - File size: {output_info['file_size'] / (1024*1024):.1f} MB")
            logger.info(f"  - Duration: {output_info['duration']:.1f} seconds")
            logger.info(f"  - Resolution: {output_info['width']}x{output_info['height']}")
            
            # Step 4: Analyze frame differences to verify zoom effects
            logger.info("\n🔍 Step 3: Analyzing zoom effects...")
            differences = analyze_frame_differences(input_video, result_path, sample_frames=10)
            
            if differences:
                significant_changes = sum(1 for d in differences if d['has_significant_change'])
                logger.info(f"📈 Frame Analysis Results:")
                logger.info(f"  - Frames analyzed: {len(differences)}")
                logger.info(f"  - Frames with significant changes: {significant_changes}")
                logger.info(f"  - Change detection rate: {(significant_changes/len(differences)*100):.1f}%")
                
                logger.info("🎯 Sample Frame Differences:")
                for diff in differences[:5]:
                    status = "🔍 ZOOM DETECTED" if diff['has_significant_change'] else "📷 NO CHANGE"
                    logger.info(f"  - Frame {diff['frame_index']} ({diff['timestamp']:.1f}s): {status}")
                    logger.info(f"    Mean diff: {diff['mean_difference']:.1f}, Max diff: {diff['max_difference']:.1f}")
                
                # Generate summary report
                logger.info("\n" + "=" * 60)
                logger.info("🎯 ZOOM FUNCTIONALITY TEST SUMMARY")
                logger.info("=" * 60)

                if face_detection_count > 0:
                    logger.info("✅ Face Detection: WORKING")
                    logger.info(f"   - Detected {face_detection_count} faces")
                else:
                    logger.info("⚠️  Face Detection: NO FACES FOUND")

                if len(zoom_timeline) > 0:
                    logger.info("✅ Zoom Timeline: GENERATED")
                    logger.info(f"   - Created {len(zoom_timeline)} zoom segments")
                else:
                    logger.info("⚠️  Zoom Timeline: NO SEGMENTS")

                if significant_changes > 0:
                    logger.info("✅ Zoom Effects: APPLIED")
                    logger.info(f"   - {significant_changes}/{len(differences)} frames show zoom effects")
                    logger.info("🎉 SUCCESS: Zoom functionality is working correctly!")

                    # Save detailed results
                    results = {
                        'input_video': input_video,
                        'output_video': result_path,
                        'analysis_time': analysis_time,
                        'processing_time': processing_time,
                        'face_detections': face_detection_count,
                        'zoom_segments': len(zoom_timeline),
                        'frames_with_zoom': significant_changes,
                        'total_frames_analyzed': len(differences),
                        'zoom_detection_rate': significant_changes/len(differences)*100
                    }
                    
                    with open('facial_ai_test_results.json', 'w') as f:
                        json.dump(results, f, indent=2)
                    
                    logger.info("📄 Detailed results saved to: facial_ai_test_results.json")
                    return True
                else:
                    logger.info("❌ Zoom Effects: NOT DETECTED")
                    logger.info("⚠️  WARNING: No zoom effects found in processed video")
                    return False
            else:
                logger.error("❌ Could not analyze frame differences")
                return False
        else:
            logger.error("❌ Output video was not created")
            return False
            
    except Exception as e:
        logger.error(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function"""
    success = test_facial_ai_with_real_video()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 FACIAL AI ZOOM TEST: SUCCESS")
        print("✅ The zoom functionality is working correctly!")
        print("📁 Check the 'videos' folder for the processed output")
        print("📄 Check 'facial_ai_test_results.json' for detailed metrics")
        print("📋 Check 'facial_ai_test.log' for complete logs")
    else:
        print("❌ FACIAL AI ZOOM TEST: FAILED")
        print("⚠️  The zoom functionality may not be working correctly")
        print("📋 Check 'facial_ai_test.log' for error details")
    print("=" * 60)
    
    return success

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
