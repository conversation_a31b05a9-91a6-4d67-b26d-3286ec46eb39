#!/usr/bin/env python3
"""
Visual Verification Script for Enhanced Facial AI
Generates comparison images and videos to demonstrate improvements
"""

import os
import sys
import cv2
import numpy as np
import logging
from pathlib import Path
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from typing import List, Dict, Any, Tuple

# Add backend to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from facial_ai_processor import FacialAIProcessor, ZoomConfig

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class VisualVerificationTester:
    """Visual verification and comparison testing"""
    
    def __init__(self):
        self.processor = FacialAIProcessor()
        self.output_dir = Path("visual_verification")
        self.output_dir.mkdir(exist_ok=True)
        
    def generate_comparison_frames(self, video_path: str, timestamps: List[float] = None) -> Dict[str, Any]:
        """Generate comparison frames showing before/after enhancement"""
        logger.info("🖼️ Generating visual comparison frames...")
        
        if timestamps is None:
            timestamps = [5.0, 15.0, 25.0, 35.0]  # Default timestamps
        
        # Test configurations
        original_config = ZoomConfig(zoom_level=1.0, include_torso=False)  # No enhancement
        enhanced_config = ZoomConfig(
            zoom_level=2.0,
            include_torso=True,
            headroom_ratio=0.15,
            torso_ratio=0.6,
            speaker_switch_threshold=0.3,
            transition_smoothness=0.5
        )
        
        comparisons = []
        
        try:
            cap = cv2.VideoCapture(video_path)
            if not cap.isOpened():
                return {"success": False, "error": "Cannot open video"}
            
            fps = cap.get(cv2.CAP_PROP_FPS)
            
            for i, timestamp in enumerate(timestamps):
                frame_number = int(timestamp * fps)
                cap.set(cv2.CAP_PROP_POS_FRAMES, frame_number)
                
                ret, frame = cap.read()
                if not ret:
                    continue
                
                # Generate enhanced version
                enhanced_frame = self._apply_enhanced_processing(frame, enhanced_config)
                
                # Create comparison image
                comparison_img = self._create_comparison_image(
                    frame, enhanced_frame, timestamp, i + 1
                )
                
                # Save comparison
                comparison_path = self.output_dir / f"comparison_{i+1:02d}_{timestamp}s.jpg"
                cv2.imwrite(str(comparison_path), comparison_img)
                
                comparisons.append({
                    "timestamp": timestamp,
                    "frame_number": frame_number,
                    "comparison_path": str(comparison_path),
                    "original_frame": frame.copy(),
                    "enhanced_frame": enhanced_frame.copy()
                })
                
                logger.info(f"✅ Generated comparison {i+1}/{len(timestamps)} at {timestamp}s")
            
            cap.release()
            
            # Generate summary comparison
            self._generate_summary_comparison(comparisons)
            
            return {
                "success": True,
                "comparisons": comparisons,
                "summary_path": str(self.output_dir / "summary_comparison.jpg")
            }
            
        except Exception as e:
            logger.error(f"Error generating comparisons: {str(e)}")
            return {"success": False, "error": str(e)}
    
    def _apply_enhanced_processing(self, frame: np.ndarray, config: ZoomConfig) -> np.ndarray:
        """Apply enhanced processing to a single frame"""
        try:
            # Simulate face detection and enhanced framing
            height, width = frame.shape[:2]
            
            # Mock face detection (center of frame for demo)
            face_center = (width // 2, height // 2)
            face_bbox = (
                width // 2 - 100,
                height // 2 - 80,
                200,
                160
            )
            
            # Apply enhanced framing
            if config.include_torso and config.zoom_level > 1.0:
                enhanced_bbox = self._calculate_enhanced_framing(face_bbox, config, width, height)
                enhanced_frame = self._apply_zoom_with_framing(frame, enhanced_bbox, config.zoom_level)
                return enhanced_frame
            else:
                return frame
                
        except Exception as e:
            logger.warning(f"Error in enhanced processing: {e}")
            return frame
    
    def _calculate_enhanced_framing(self, face_bbox: Tuple[int, int, int, int], 
                                  config: ZoomConfig, frame_width: int, frame_height: int) -> Tuple[int, int, int, int]:
        """Calculate enhanced framing for demo"""
        x, y, w, h = face_bbox
        
        # Professional framing with torso
        torso_extension = int(h * config.torso_ratio * 2)
        headroom = int(h * config.headroom_ratio)
        
        # Calculate new dimensions
        new_height = h + torso_extension + headroom
        new_width = int(new_height * 0.75)  # 4:3 aspect ratio
        
        # Center on face
        new_x = max(0, x + w//2 - new_width//2)
        new_y = max(0, y - headroom)
        
        # Ensure within bounds
        new_x = min(new_x, frame_width - new_width)
        new_y = min(new_y, frame_height - new_height)
        
        return (new_x, new_y, new_width, new_height)
    
    def _apply_zoom_with_framing(self, frame: np.ndarray, bbox: Tuple[int, int, int, int], zoom_level: float) -> np.ndarray:
        """Apply zoom with enhanced framing"""
        try:
            height, width = frame.shape[:2]
            x, y, w, h = bbox
            
            # Ensure bbox is within frame
            x = max(0, min(x, width - w))
            y = max(0, min(y, height - h))
            w = min(w, width - x)
            h = min(h, height - y)
            
            # Crop the region
            cropped = frame[y:y+h, x:x+w]
            
            # Resize to original frame size
            zoomed = cv2.resize(cropped, (width, height), interpolation=cv2.INTER_LANCZOS4)
            
            return zoomed
            
        except Exception as e:
            logger.warning(f"Error applying zoom: {e}")
            return frame
    
    def _create_comparison_image(self, original: np.ndarray, enhanced: np.ndarray, 
                               timestamp: float, frame_num: int) -> np.ndarray:
        """Create side-by-side comparison image"""
        height, width = original.shape[:2]
        
        # Create comparison canvas
        comparison = np.zeros((height, width * 2 + 20, 3), dtype=np.uint8)
        
        # Add original frame
        comparison[:, :width] = original
        
        # Add enhanced frame
        comparison[:, width + 20:] = enhanced
        
        # Add labels
        font = cv2.FONT_HERSHEY_SIMPLEX
        font_scale = 1.0
        color = (255, 255, 255)
        thickness = 2
        
        # Original label
        cv2.putText(comparison, "Original", (10, 40), font, font_scale, color, thickness)
        
        # Enhanced label
        cv2.putText(comparison, "Enhanced (Torso + Dynamic)", (width + 30, 40), font, font_scale, color, thickness)
        
        # Timestamp
        cv2.putText(comparison, f"Time: {timestamp}s", (10, height - 20), font, 0.7, color, thickness)
        
        # Frame number
        cv2.putText(comparison, f"Frame: {frame_num}", (width + 30, height - 20), font, 0.7, color, thickness)
        
        return comparison
    
    def _generate_summary_comparison(self, comparisons: List[Dict[str, Any]]):
        """Generate a summary image with all comparisons"""
        if not comparisons:
            return
        
        try:
            # Create a grid of comparisons
            num_comparisons = len(comparisons)
            cols = 2
            rows = (num_comparisons + 1) // 2
            
            # Load first comparison to get dimensions
            first_comparison = cv2.imread(comparisons[0]["comparison_path"])
            comp_height, comp_width = first_comparison.shape[:2]
            
            # Create summary canvas
            summary_height = comp_height * rows + 50
            summary_width = comp_width * cols + 50
            summary = np.zeros((summary_height, summary_width, 3), dtype=np.uint8)
            
            # Add title
            font = cv2.FONT_HERSHEY_SIMPLEX
            title = "Enhanced Facial AI - Visual Comparison Summary"
            cv2.putText(summary, title, (50, 30), font, 1.0, (255, 255, 255), 2)
            
            # Add comparisons to grid
            for i, comp in enumerate(comparisons):
                row = i // cols
                col = i % cols
                
                y_start = 50 + row * comp_height
                x_start = 25 + col * comp_width
                
                # Load and resize comparison if needed
                comp_img = cv2.imread(comp["comparison_path"])
                if comp_img is not None:
                    # Resize to fit if necessary
                    comp_img = cv2.resize(comp_img, (comp_width, comp_height))
                    summary[y_start:y_start + comp_height, x_start:x_start + comp_width] = comp_img
            
            # Save summary
            summary_path = self.output_dir / "summary_comparison.jpg"
            cv2.imwrite(str(summary_path), summary)
            
            logger.info(f"📊 Summary comparison saved to: {summary_path}")
            
        except Exception as e:
            logger.error(f"Error generating summary: {e}")
    
    def test_speaker_switching_visualization(self, video_path: str) -> Dict[str, Any]:
        """Test and visualize speaker switching functionality"""
        logger.info("🎭 Testing speaker switching visualization...")
        
        try:
            # Analyze video for speaker segments
            config = ZoomConfig(
                zoom_level=1.8,
                include_torso=True,
                speaker_switch_threshold=0.3,
                transition_smoothness=0.5
            )
            
            analysis = self.processor.analyze_video(video_path, config)
            speaker_segments = analysis.get("speaker_segments", [])
            
            if not speaker_segments:
                return {"success": False, "error": "No speaker segments detected"}
            
            # Generate visualization
            viz_path = self._create_speaker_timeline_visualization(speaker_segments, video_path)
            
            return {
                "success": True,
                "speaker_segments": len(speaker_segments),
                "visualization_path": viz_path,
                "analysis": analysis
            }
            
        except Exception as e:
            logger.error(f"Error in speaker switching test: {e}")
            return {"success": False, "error": str(e)}
    
    def _create_speaker_timeline_visualization(self, speaker_segments: List[Dict[str, Any]], video_path: str) -> str:
        """Create timeline visualization of speaker switches"""
        try:
            fig, ax = plt.subplots(figsize=(12, 6))
            
            # Plot speaker segments
            colors = ['red', 'blue', 'green', 'orange', 'purple']
            
            for i, segment in enumerate(speaker_segments):
                start_time = segment.get("start_time", 0)
                end_time = segment.get("end_time", 0)
                speaker_id = segment.get("speaker_id", 0)
                
                color = colors[speaker_id % len(colors)]
                
                # Draw segment
                ax.barh(speaker_id, end_time - start_time, left=start_time, 
                       height=0.8, color=color, alpha=0.7, 
                       label=f'Speaker {speaker_id}' if i == 0 or speaker_id not in [s.get("speaker_id") for s in speaker_segments[:i]] else "")
            
            ax.set_xlabel('Time (seconds)')
            ax.set_ylabel('Speaker ID')
            ax.set_title('Dynamic Speaker Switching Timeline')
            ax.legend()
            ax.grid(True, alpha=0.3)
            
            # Save visualization
            viz_path = self.output_dir / "speaker_timeline.png"
            plt.savefig(viz_path, dpi=300, bbox_inches='tight')
            plt.close()
            
            logger.info(f"📈 Speaker timeline saved to: {viz_path}")
            return str(viz_path)
            
        except Exception as e:
            logger.error(f"Error creating timeline visualization: {e}")
            return ""

def main():
    """Main visual verification execution"""
    # Look for test video
    test_videos = [
        "videos/videoplayback (1).mp4",
        "videos/videoplayback (2).mp4",
        "../test_videos/sample.mp4"
    ]
    
    test_video = None
    for video_path in test_videos:
        if os.path.exists(video_path):
            test_video = video_path
            break
    
    if not test_video:
        logger.error("❌ No test video found for visual verification")
        return
    
    logger.info(f"🎥 Using test video: {test_video}")
    
    # Run visual verification tests
    tester = VisualVerificationTester()
    
    # Generate comparison frames
    logger.info("\n🖼️ Generating visual comparisons...")
    comparison_result = tester.generate_comparison_frames(test_video)
    
    if comparison_result["success"]:
        logger.info("✅ Visual comparisons generated successfully")
        logger.info(f"📁 Check output directory: {tester.output_dir}")
    else:
        logger.error(f"❌ Visual comparison failed: {comparison_result.get('error')}")
    
    # Test speaker switching visualization
    logger.info("\n🎭 Testing speaker switching visualization...")
    speaker_result = tester.test_speaker_switching_visualization(test_video)
    
    if speaker_result["success"]:
        logger.info(f"✅ Speaker switching test completed - {speaker_result['speaker_segments']} segments detected")
    else:
        logger.error(f"❌ Speaker switching test failed: {speaker_result.get('error')}")
    
    logger.info("\n🎉 Visual verification tests completed!")
    logger.info(f"📁 All outputs saved to: {tester.output_dir}")

if __name__ == "__main__":
    main()
