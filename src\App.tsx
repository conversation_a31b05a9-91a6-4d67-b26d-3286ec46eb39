import React from "react";
import { Routes, Route } from "react-router-dom";
import { AuthProvider } from "./context/AuthContext";
import ErrorBoundary from "./components/ErrorBoundary";
import Navbar from "./components/Navbar";
import ConfigurationBanner from "./components/ConfigurationBanner";
import Index from "./pages/Index";
import Login from "./pages/Login";
import Register from "./pages/Register";
import Onboarding from "./pages/Onboarding";
import Profile from "./pages/Profile";
import Dashboard from "./pages/Dashboard";
import VideoCreator from "./pages/VideoCreator";
import VideoEditor from "./pages/VideoEditor";
import Gallery from "./pages/Gallery";
import NotFound from "./pages/NotFound";
import SmartClipper from "./pages/SmartClipper";
import AvatarCreator from "./pages/AvatarCreator";
import ScriptGenerator from "./pages/ScriptGenerator";
import VideoGenerator from "./pages/VideoGenerator";
import CloudinaryConfig from "./pages/CloudinaryConfig";

import Calendar from "./pages/Calendar";
import Settings from "./pages/Settings";
import Analytics from "./pages/Analytics";
import ClipResults from "./pages/ClipResults";
import ClipEditor from "./pages/ClipEditor";
import Support from "./pages/Support";
import AuthTest from "./pages/AuthTest";
import EnvCheck from "./pages/EnvCheck";
import AuthDebug from "./pages/AuthDebug";
import SupabaseDebug from "./pages/SupabaseDebug";
import { Toaster } from "@/components/ui/toaster";
import "./App.css";
import DashboardLayout from "./components/Dashboard";
import HomePage from "./pages/HomePage";
import Contact from "./pages/Contact";
import PaymentPlan from "./pages/paymentplan";
import PaymentSuccess from "./pages/PaymentSuccess";
import AuthCallback from "./pages/AuthCallback";
import PrivacyandPolicy from "./pages/PrivacyandPolicy";
import FacialAI from "./pages/FacialAI";

function App() {
  return (
    <ErrorBoundary>
      <AuthProvider>
        <div className="min-h-screen flex flex-col">
          <Navbar />
          <main className="flex-1">
            <ConfigurationBanner />
            <ErrorBoundary>
              <Routes>
                {/* Public Routes */}
                <Route path="/" element={<HomePage />} />
                <Route path="/landing" element={<Index />} />
                <Route path="/login" element={<Login />} />
                <Route
                  path="/privacyandpolicy"
                  element={<PrivacyandPolicy />}
                />
                <Route path="/register" element={<Register />} />
                <Route path="/auth-test" element={<AuthTest />} />
                <Route path="/env-check" element={<EnvCheck />} />
                <Route path="/auth-debug" element={<AuthDebug />} />
                <Route path="/supabase-debug" element={<SupabaseDebug />} />
                <Route path="/contact" element={<Contact />} />
                <Route path="/payment" element={<PaymentPlan />} />
                <Route path="/payment-success" element={<PaymentSuccess />} />
                <Route path="/auth-callback" element={<AuthCallback />} />

                {/* Core Features - Public Access */}

                {/* Core Video Processing - Public Access */}
                <Route
                  path="/smart-clipper"
                  element={
                    <ErrorBoundary>
                      <SmartClipper />
                    </ErrorBoundary>
                  }
                />

                {/* Protected Routes - Require Authentication */}
                <Route path="/onboarding" element={<Onboarding />} />
                <Route path="/profile" element={<Profile />} />
                <Route path="/dashboard" element={<Dashboard />} />
                <Route path="/create" element={<VideoCreator />} />
                <Route path="/gallery" element={<Gallery />} />
                <Route path="/video-generator" element={<VideoGenerator />} />
                <Route path="/video-editor" element={<VideoEditor />} />
                <Route path="/editor" element={<VideoCreator />} />
                <Route path="/cloudinary" element={<CloudinaryConfig />} />

                <Route path="/calendar" element={<Calendar />} />
                <Route path="/settings" element={<Settings />} />
                <Route path="/analytics" element={<Analytics />} />
                <Route path="/facial-ai" element={<FacialAI />} />

                {/* New Clip Management Routes - Protected */}
                <Route
                  path="/clip-results"
                  element={
                    <ErrorBoundary>
                      <ClipResults />
                    </ErrorBoundary>
                  }
                />
                <Route
                  path="/clip-editor/:clipId"
                  element={
                    <ErrorBoundary>
                      <ClipEditor />
                    </ErrorBoundary>
                  }
                />

                <Route
                  path="/credits"
                  element={
                    <ErrorBoundary>
                      <PaymentPlan />
                    </ErrorBoundary>
                  }
                />

                {/* Support Route - Protected */}
                <Route
                  path="/support"
                  element={
                    <ErrorBoundary>
                      <Support />
                    </ErrorBoundary>
                  }
                />

                {/* 404 Route */}
                <Route path="*" element={<NotFound />} />
              </Routes>
            </ErrorBoundary>
          </main>
          <Toaster />
        </div>
      </AuthProvider>
    </ErrorBoundary>
  );
}

export default App;
