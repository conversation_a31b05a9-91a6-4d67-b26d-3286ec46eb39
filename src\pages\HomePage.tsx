import React, { useState, useEffect, useRef } from "react";
import { Navigate, Link } from "react-router-dom";
import DashboardLayout from "@/components/Dashboard";
import { useAuth } from "@/context/AuthContext";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Scissors,
  Upload,
  Zap,
  Gift,
  Link2,
  Check,
  Video,
  ChevronDown,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  type UserClip,
  type UserStats,
} from "@/services/userService";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle
} from "@/components/ui/dialog";
import { Toggle } from "@/components/ui/toggle";
import { supabase } from "@/lib/supabase";
import { toast } from "@/hooks/use-toast";

// ------- Utils -------
function secondsToTime(secs: number) {
  const h = Math.floor(secs / 3600);
  const m = Math.floor((secs % 3600) / 60);
  const s = Math.floor(secs % 60);
  return [h, m, s].map(n => String(n).padStart(2, '0')).join(':');
}

// ------- Component ---------

const previewPlaceholder =
  "https://pplx-res.cloudinary.com/image/private/user_uploads/82268783/10964ce1-e442-4aef-8db8-2e8d21bd7576/Screenshot-2025-07-19-at-8.53.14-PM.jpg";

const GetClipsButton = ({
  isLoading,
  onClick,
}: {
  isLoading?: boolean;
  onClick?: () => void;
}) => (
  <button
    type="button"
    className="
      w-full flex items-center justify-center gap-4 py-5 rounded-xl
      font-bold text-white text-2xl
      bg-gradient-to-r from-[#a445e5] to-[#3793e0]
      shadow-lg
      transition-all duration-200
      hover:brightness-105 hover:scale-[1.01]
      focus:brightness-105 focus:scale-[1.01]
      active:scale-95
      select-none border-none outline-none
      min-h-16
    "
    onClick={onClick}
    disabled={isLoading}
  >
    {isLoading ? (
      <>
        <div className="animate-spin rounded-full h-6 w-6 border-2 border-b-0 border-white mr-2" />
        Processing...
      </>
    ) : (
      <>
        <Scissors className="w-1 h-8" />
        <span>Get Clips</span>
      </>
    )}
  </button>
);


const selectBase =
  "appearance-none bg-transparent text-white font-semibold pl-0 pr-7 h-8 border-none outline-none min-w-[72px]";
const rowLabel = "text-gray-300 text-base font-medium mr-1";

const HomePage = () => {
  const { isAuthenticated, user, isLoading, markWelcomeModalAsSeen } = useAuth();
  const [showWelcomeModal, setShowWelcomeModal] = useState(false);
  const [activeTab, setActiveTab] = useState("overview");
  const [clips, setClips] = useState<UserClip[]>([]);
  const [stats, setStats] = useState<UserStats>({
    totalViews: 0,
    totalVideos: 0,
    totalClips: 0,
    totalSubscribers: 0,
    watchTime: 0,
    credits: 0,
  });
  const [isLoadingData, setIsLoadingData] = useState(true);
  const [addEmojis, setAddEmojis] = useState(false);
  const [videoUrl, setVideoUrl] = useState("");
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [activeInputTab, setActiveInputTab] = useState("url");
  const [isUploading, setIsUploading] = useState(false);
  const [processedVideoResult, setProcessedVideoResult] = useState<{ url: string } | null>(null);

  // --- New AI Clipping block state ---
  const [aiClippingMode, setAiClippingMode] = useState<'ai' | 'none'>('ai');
  const [clipModel, setClipModel] = useState("Auto");
  const [genre, setGenre] = useState("Auto");
const [clipLength, setClipLength] = useState("< 30 sec");

  const [moments, setMoments] = useState("");

  const [videoDuration, setVideoDuration] = useState<number>(0);
  const [range, setRange] = useState<[number, number]>([0, 0]); // [startSec, endSec]
  const videoRef = useRef<HTMLVideoElement | null>(null);

  const clipModelOptions = ["Auto", "Clips V2", "Clips V1"];
  const genreOptions = ["Auto", "Podcast", "Gaming", "Educational", "Interview"];
  const clipLengthOptions = [
  "Auto (0m–3m)",
  "<30s",
  "30s – 59s",
  "60s – 89s",
  "90s – 3m",
  "3m – 5m",
  "5m – 10m",
];

  useEffect(() => {
    if (isAuthenticated && user && !user.seen_welcome) {
      setShowWelcomeModal(true);
      markWelcomeModalAsSeen();
    }
  }, [user, isAuthenticated, markWelcomeModalAsSeen]);

  useEffect(() => {
    if (isAuthenticated && user) {
      fetchAllUserData();
    }
  }, [isAuthenticated, user]);

  // When video URL changes, reset duration/range
  useEffect(() => {
    setVideoDuration(0);
    setRange([0, 0]);
  }, [videoUrl]);

  // When video loads, update duration/range
  const handleVideoLoadedMetadata = (
    e: React.SyntheticEvent<HTMLVideoElement, Event>
  ) => {
    const duration = e.currentTarget.duration || 0;
    setVideoDuration(duration);
    setRange([0, duration]);
  };

  const handleUpload = async (file: File) => {
    if (!file) return;
    setIsUploading(true);
    setError(null);
    try {
      const formData = new FormData();
      formData.append("file", file);
      const response = await fetch("http://localhost:8000/compress-and-upload/", {
        method: "POST",
        body: formData,
      });
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || "Processing failed on the server.");
      }
      const result = await response.json();
      const compressedUrl = result.url;
      setVideoUrl(compressedUrl);
      toast({ title: "Upload Successful", description: "Your video was compressed and uploaded." });
      setActiveInputTab("url");
    } catch (err: any) {
      setError(err.message);
      toast({ title: "Upload Failed", description: err.message, variant: "destructive" });
    } finally {
      setIsUploading(false);
    }
  };

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) handleUpload(file);
  };

  const handleRangeChange = (e: React.ChangeEvent<HTMLInputElement>, which: "start" | "end") => {
    const value = Number(e.target.value);
    if (which === "start") setRange([Math.min(value, range[1]-1), range[1]]);
    else setRange([range[0], Math.max(value, range[0]+1)]);
  };

  const handleGetClips = async () => {
    if (!videoUrl) {
      setError("Please provide a video URL or upload a file.");
      return;
    }
    setError(null);
    setIsProcessing(true);
    setProcessedVideoResult(null);

    try {
      const payload = {
        video_url: videoUrl,
        ai_clipping: aiClippingMode === "ai",
        clip_model: clipModel,
        genre,
        clip_length: clipLength,
        specific_moments: moments,
        processing_timeframe: aiClippingMode === "none" ? range : undefined,
        options: {
          add_subtitles: true,
          add_emojis: addEmojis,
          create_short_form: true,
          platforms: ["tiktok"],
          subtitle_style: "viral_style",
          max_short_clips: 5,
        },
      };
      const formData = new FormData();
      formData.append("request", JSON.stringify(payload));
      const response = await fetch("http://localhost:8000/advanced-process", {
        method: "POST",
        headers: { Authorization: `Bearer ${localStorage.getItem("auth_token")}` },
        body: formData,
      });
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || "Processing failed");
      }
      const data = await response.json();
      if (!data.success || !data.processed_videos?.with_ai_subtitles) {
        throw new Error(data.message || "Failed to retrieve processed video from API response.");
      }
      setProcessedVideoResult({ url: data.processed_videos.with_ai_subtitles });
      setActiveTab("result");
      const { data: { session } } = await supabase.auth.getSession();
      if (!session?.user?.id)
        throw new Error("User not authenticated for saving data.");
      const transcript = data.metadata?.transcript;
      if (!transcript || transcript.length === 0) {
        throw new Error("Transcript data missing in API response, cannot save clip details.");
      }
      const insertData = {
        user_id: session.user.id,
        url: data.processed_videos.with_ai_subtitles,
        text: transcript.map((t: { word: string }) => t.word).join(" "),
        start_time: transcript[0].start,
        end_time: transcript[transcript.length - 1].end,
        score: 0,
        feedback: "None",
        platform: "Cloudinary",
      };
      const { error: insertError } = await supabase.from("myclip").insert([insertData]);
      if (insertError) throw insertError;
      toast({ title: "Success!", description: "Video processed and saved. See the result below." });
      fetchAllUserData();
    } catch (err: any) {
      const errorMessage = err.message || "An unknown error occurred.";
      setError(errorMessage);
      toast({ title: "Processing Failed", description: errorMessage, variant: "destructive" });
    } finally {
      setIsProcessing(false);
    }
  };

  const fetchAllUserData = async () => {
    if (!user) return;
    setIsLoadingData(true);
    try {
      const { data: rawClips, error: clipsError, count } = await supabase
        .from("myclip")
        .select("id, url, created_at, platform", { count: "exact" })
        .eq("user_id", user.id)
        .order("created_at", { ascending: false })
        .limit(20);
      if (clipsError) throw clipsError;
      const formattedClips: UserClip[] = (rawClips || []).map((clip) => ({
        id: clip.id,
        thumbnail: clip.url.trim().replace(/\.mp4$/, ".jpg"),
        title: `Clip from ${new Date(clip.created_at).toLocaleDateString()}`,
        status: "published",
        duration: "0:30",
        platform: clip.platform || "Cloudinary",
        views: 0,
        likes: 0,
        comments: 0,
        createdAt: new Date(clip.created_at).toLocaleDateString(),
      }));
      setClips(formattedClips);
      setStats((prev) => ({
        ...prev,
        totalClips: count || 0,
        credits: user.credits || 0,
      }));
    } catch (error) {
      console.error("Error fetching user data:", error);
    } finally {
      setIsLoadingData(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (!isAuthenticated) return <Navigate to="/landing" />;

  const performanceStats = [
    {
      title: "Clips Generated",
      value: stats.totalClips.toString(),
      icon: Scissors,
      color: "text-purple-500",
    },
    {
      title: "Credits Available",
      value: stats.credits.toString(),
      icon: Zap,
      color: "text-yellow-500",
    },
  ];

  // ---- Main Render ----
  return (
    <DashboardLayout>
      <Dialog open={showWelcomeModal} onOpenChange={setShowWelcomeModal}>
        <DialogContent className="sm:max-w-md text-center">
          <DialogHeader>
            <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-green-100 mb-4">
              <Gift className="h-6 w-6 text-green-600" aria-hidden="true" />
            </div>
            <DialogTitle className="text-2xl font-semibold">
              Welcome to SmartClips!
            </DialogTitle>
            <DialogDescription className="mt-2 text-base text-muted-foreground">
              We're excited to have you on board! To get you started, we've{" "}
              <strong>added 10 free credits</strong> to your account. Enjoy creating!
            </DialogDescription>
          </DialogHeader>
          <DialogFooter className="sm:justify-center">
            <Button type="button" onClick={() => setShowWelcomeModal(false)}>
              Get Started
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* <div className="flex flex-col items-center min-h-screen w-full bg-ultra-dark pt-2 border"> */}
          {/* Video/Image Preview (now hidden if there's no videoUrl) */}
          {videoUrl && (
            <div className="flex flex-col w-full items-center px-0 pt-6">
              <div className="rounded-lg overflow-hidden mb-3 w-[260px] h-[146px] sm:w-[340px] sm:h-[192px] bg-black flex items-center justify-center border-2 border-[#282447]">
                <video
                  ref={videoRef}
                  src={videoUrl}
                  onLoadedMetadata={handleVideoLoadedMetadata}
                  className="object-cover w-full h-full"
                  controls={false}
                  poster={previewPlaceholder}
                />
              </div>
              {/* Copyright warning */}
              <div className="w-full flex justify-center my-1">
                <div className="bg-[#22223c] bg-opacity-80 px-4 py-1 rounded text-xs text-gray-200 font-semibold shadow-sm text-center">
                  Using video you don't own may violate copyright laws. By continuing, you confirm this is your own original content.
                </div>
              </div>
            </div>
          )}

          {/* ---- Create a New Clip UI ---- */}
          <div className="p-6 sm:p-8 pt-2">
            <div className="text-center mb-8">
                <div className="inline-flex items-center justify-center w-16 h-16 mb-4 rounded-full bg-gradient-to-br from-purple-600 to-blue-500">
                <Zap className="w-8 h-8 text-white animate-pulse" />
                </div>
              <h2 className="text-2xl font-bold text-gray-100">
                Create a New Clip
              </h2>
              <p className="text-gray-400 mt-1">
                Paste a URL or upload a video to get started.
              </p>
            </div>
            <Tabs
              value={activeInputTab}
              onValueChange={setActiveInputTab}
              className="w-full"
            >
              <TabsList className="grid w-full grid-cols-2 bg-[#2d2d2d] border-gray-700">
                <TabsTrigger value="url">From URL</TabsTrigger>
                <TabsTrigger value="upload">Upload File</TabsTrigger>
              </TabsList>
              <TabsContent value="url" className="mt-4">
                <div className="relative">
                  <Link2 className="absolute left-4 top-1/2 -translate-y-1/2 h-5 w-5 text-gray-500" />
                  <Input
                    placeholder="https://x.com/user/status/12345"
                    className="pl-12 h-14 bg-[#23233a] border-2 border-gray-700 focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-base text-white placeholder:text-gray-500 rounded-lg"
                    value={videoUrl}
                    onChange={(e) => setVideoUrl(e.target.value)}
                  />
                </div>
              </TabsContent>
              <TabsContent value="upload" className="mt-4">
                <label className="flex flex-col items-center justify-center w-full h-32 border-2 border-gray-600 border-dashed rounded-lg cursor-pointer bg-[#23233a] hover:bg-gray-800">
                  <div className="flex flex-col items-center justify-center text-center">
                    {isUploading ? (
                      <>
                        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-500 mb-2"></div>
                        <p className="text-sm text-gray-400">Uploading...</p>
                      </>
                    ) : (
                      <>
                        <Upload className="w-8 h-8 mb-2 text-gray-500" />
                        <p className="text-sm text-gray-400">
                          <span className="font-semibold text-purple-400">
                            Click to upload
                          </span>{" "}
                          or drag & drop
                        </p>
                        <p className="text-xs text-gray-500">
                          MP4, MOV (MAX. 500MB)
                        </p>
                      </>
                    )}
                  </div>
                  <input
                    id="file-upload"
                    type="file"
                    className="hidden"
                    onChange={handleFileChange}
                    accept="video/mp4,video/quicktime,video/avi"
                    disabled={isUploading}
                  />
                </label>
              </TabsContent>
            </Tabs>
            {/* ---- AI Clipping Block ---- */}
            <div className="flex gap-7 text-base font-semibold text-white mb-5 mt-8">
              <button
                className={`px-2 pb-1 pt-0 border-b-2 transition ${
                  aiClippingMode === "ai"
                    ? "border-white text-white"
                    : "border-transparent text-gray-400"
                }`}
                onClick={() => setAiClippingMode("ai")}
              >
                AI clipping
              </button>
              <button
                className={`px-2 pb-1 pt-0 border-b-2 transition ${
                  aiClippingMode === "none"
                    ? "border-white text-white"
                    : "border-transparent text-gray-400"
                }`}
                onClick={() => setAiClippingMode("none")}
              >
                Do not clip
              </button>
            </div>
            {/* --- Do not clip UI (Slider block) --- */}
            {aiClippingMode === "none" && (
              <div className="rounded-lg bg-[#23233a] p-5 mb-7">
                <div className="text-gray-200 mb-4">
                  We will not clip your videos and will preserve their original length.
                </div>
                <div className="flex items-center gap-4 mb-2">
                  <span className="text-gray-300">Processing timeframe</span>
                  <span className="bg-gradient-purple-blue text-green-100 font-semibold rounded px-3 py-1 text-sm">
                    Credit saver
                  </span>
                </div>
                <div className="w-full flex flex-col gap-2">
                  <div className="flex items-center justify-between mb-2">
                    <span className="bg-[#1b1b28] rounded-lg px-2 py-1 text-gray-100 font-mono text-sm">
                      {secondsToTime(range[0])}
                    </span>
                    <span className="bg-[#281b1b] rounded-lg px-2 py-1 text-gray-100 font-mono text-sm">
                      {secondsToTime(videoDuration || range[1])}
                    </span>
                  </div>
                  {/* Range bar (two range inputs, visually a double slider) */}
                  <div className="relative w-full flex items-center h-6">
                    <input
                      type="range"
                      min={0}
                      max={Math.max(videoDuration, 1) - 1}
                      value={range[0]}
                      onChange={(e) => handleRangeChange(e, "start")}
                      disabled={videoDuration <= 1}
                      className="w-full accent-blue-500 h-2"
                      style={{ position: "absolute", zIndex: 2 }}
                    />
                    <input
                      type="range"
                      min={1}
                      max={Math.max(videoDuration, 1)}
                      value={range[1]}
                      onChange={(e) => handleRangeChange(e, "end")}
                      disabled={videoDuration <= 1}
                      className="w-full accent-blue-300 h-2"
                      style={{ position: "absolute", zIndex: 2 }}
                    />
                    {/* Slider track (visual) */}
                    <div
                      className="absolute h-1 w-full rounded-full"
                      style={{
                        background:
                          "linear-gradient(90deg, #a445e5 0%, #3793e0 100%)",
                        opacity: 0.2,
                      }}
                    />
                  </div>
                </div>
              </div>
            )}
            {/* --- Clipping selects & prompt, only if "ai" --- */}
            {aiClippingMode === "ai" && (
              <>
                <div className="flex flex-row gap-3 items-center mb-5 ml-1">
                  {/* Clip model */}
                  {/* Clip Length */}
                  <label className="flex items-center gap-1">
                    <span className={rowLabel}>Clip Length</span>
                    <div className="relative">
                      <select
                        value={clipLength}
                        onChange={(e) => setClipLength(e.target.value)}
                        className={selectBase}
                        style={{ width: 138 }}
                      >
                        {clipLengthOptions.map((o) => (
                          <option key={o} value={o} className="text-black">{o}</option>
                        ))}
                      </select>
                      <ChevronDown className="absolute right-1 top-1/2 -translate-y-1/2 w-4 h-4 text-gray-300 pointer-events-none" />
                    </div>
                  </label>
                </div>
                <div className="flex items-center justify-between mb-2 px-[2px]">
                  <span className="font-semibold text-white text-base">
                    Include specific moments
                  </span>
                  <span className="text-xs text-gray-200 font-medium">
                    Not sure how to prompt?{" "}
                    <a
                      href="https://help.smartclips.ai/prompts"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="underline text-[#f8e9ba] font-semibold"
                    >
                      learn more
                    </a>
                  </span>
                </div>
                <div className="relative w-full">
                <input
                  value={moments}
                  onChange={(e) => setMoments(e.target.value)}
                  className={`
                    w-full h-12 rounded-lg px-4 py-3
                    font-medium text-base outline-none transition-all duration-200
                    bg-[#23233a] bg-opacity-70 border-2
                    focus:border-blue-500 focus:ring-2 focus:ring-blue-500/30
                    hover:bg-[#23233a]
                    text-white placeholder-gray-400
                    shadow-[0_0_0_0_white]
                  `}
                  placeholder="Example: find moments when we talked about the playoffs"
                  style={{ borderWidth: 2 }}
                />
              </div>

              </>
            )}
            {/* -- Emoji Toggle -- */}
            <div className="space-y-6 mt-10">
              <Toggle
              pressed={addEmojis}
              onPressedChange={setAddEmojis}
              className={
                "w-full h-14 flex items-center justify-between p-4 rounded-lg transition-colors border shadow-sm" +
                (addEmojis
                  ? " bg-gradient-purple-blue text-white border-purple-600"
                  : " bg-white/5 border-gray-700 text-gray-300 hover:bg-white/10")
              }
            >
              {/* Left-side emoji */}
              <span className="text-2xl mr-4 flex-shrink-0">✨</span>

              {/* Centered caption */}
              <div className="flex-grow flex flex-col items-center justify-center">
                <span className="font-semibold text-base">AI Emoji Captions</span>
                <p className="text-xs italic text-gray-400">
                  Automatically add relevant emojis
                </p>
              </div>

              {/* Right-side checkmark */}
              <div
                className={
                  "w-7 h-7 ml-4 flex-shrink-0 flex items-center justify-center rounded-full border-2 transition-colors duration-200" +
                  (addEmojis
                    ? " bg-emerald-500 border-emerald-600 shadow"
                    : " bg-transparent border-gray-600")
                }
                aria-label={addEmojis ? "Enabled" : "Disabled"}
              >
                <Check className={`h-4 w-4 ${addEmojis ? "text-white" : "text-gray-300"}`} />
              </div>
            </Toggle>
            </div>
            {error && (
              <p className="text-sm text-red-500 text-center mt-4">{error}</p>
            )}
          </div>
          <div className="w-full flex justify-center my-6">
          <div className="w-full max-w-2xl rounded-xl shadow-xl border border-[#282447] bg-[#181825] p-0 pt-0">
            <div className="rounded-t-xl">
              <GetClipsButton
                isLoading={isProcessing || isUploading}
                onClick={handleGetClips}
              />
            </div>
          </div>
        </div>

        <br />
        {/* ========== Performance Stats & Clips List/Tabs ========== */}
        <div className="w-full px-4 mt-12">
  {/* Centered Overview Header */}
  <div className="flex flex-col items-center mb-10">
    <h1 className="text-2xl md:text-3xl font-bold text-white mb-2 tracking-tight">
      Overview
    </h1>
  </div>

  {/* Stats Grid */}
  <div className="grid grid-cols-2 lg:grid-cols-4 gap-5 md:gap-8 mb-12">
    {performanceStats.map((stat, index) => (
      <Card
        key={index}
        className="bg-[#0d0d12] border border-[#181820] shadow-sm hover:shadow-md transition-all"
      >
        <CardContent className="flex flex-col items-center p-5 md:p-7">
          <div className="flex items-center justify-center w-12 h-12 mb-5 rounded-full bg-[#181820]">
            <stat.icon className={`h-7 w-7 ${stat.color}`} />
          </div>
          <p className="text-2xl md:text-3xl font-extrabold text-white mb-1 text-center leading-snug">
            {stat.value}
          </p>
          <p className="text-xs md:text-sm text-gray-400 text-center">
            {stat.title}
          </p>
        </CardContent>
      </Card>
    ))}
  </div>

  {/* Tabs for Overview/Result */}
  <Tabs
    value={activeTab}
    onValueChange={setActiveTab}
    className="space-y-4 md:space-y-6"
  >
    <TabsContent value="overview" className="space-y-6">
      <div>
        <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-4 gap-2">
          <h2 className="text-lg md:text-xl font-semibold text-white">
            Latest uploads
          </h2>
          <Link to="/clip-results" className="md:ml-auto">
            <Button variant="outline" size="sm">
              View all
            </Button>
          </Link>
        </div>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 md:gap-4">
          {clips.length > 0 ? (
            clips.slice(0, 4).map((clip) => (
              <Card
                key={clip.id}
                className="group hover:shadow-md transition-all bg-[#16161d] border border-[#20202a]"
              >
                <div className="relative">
                  <img
                    src={clip.thumbnail}
                    alt={clip.title}
                    className="w-full h-32 object-cover"
                  />
                </div>
                <CardContent className="p-3">
                  <h3 className="font-medium text-sm text-white line-clamp-2">
                    {clip.title}
                  </h3>
                </CardContent>
              </Card>
            ))
          ) : (
            <p className="col-span-4 text-gray-400 text-center py-4">
              No clips found. Create one above to get started!
            </p>
          )}
        </div>
      </div>
    </TabsContent>
    {processedVideoResult && (
      <TabsContent value="result">
        <Card className="bg-[#16161d] border border-[#22222a]">
          <CardHeader>
            <CardTitle className="text-white">Your Processed Video</CardTitle>
          </CardHeader>
          <CardContent>
            <video
              src={processedVideoResult.url}
              controls
              className="w-full rounded-lg aspect-video bg-black"
            />
            <p className="text-sm text-gray-400 mt-4">
              This video has been processed and saved. You can find it
              later in your "Latest uploads" or content library.
            </p>
          </CardContent>
        </Card>
      </TabsContent>
    )}
  </Tabs>
</div>
      {/* </div> */}
    </DashboardLayout>
  );
};

export default HomePage;