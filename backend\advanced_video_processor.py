"""
Advanced Video Processing Module for SmartClips
Provides comprehensive video enhancement with subtitles, emojis, clipart, and short-form content creation
Updated with enhanced TikTok-style subtitle system, PROPER WORD SPACING, and AI-POWERED FEATURES
"""
import whisper
import pysrt
from transformers import pipeline
import openai
import emoji
from textblob import TextBlob
import spacy
import nltk
import os
import re
import json
import tempfile
import subprocess
import logging
from typing import List, Dict, Any, Tuple, Optional
from pathlib import Path
import requests
from datetime import datetime, timedelta
import shutil
import traceback
from pydub import AudioSegment
import speech_recognition as sr
import torch
import gc
import whisperx
import moviepy.video.fx.all as vfx
# Core processing libraries
import cv2
import numpy as np
from PIL import Image, ImageDraw, ImageFont
import librosa
from moviepy.editor import VideoFileClip, AudioFileClip, ImageClip, CompositeVideoClip, TextClip, ColorClip, VideoClip
import moviepy.config as mpconfig
# if os.name == 'nt':
#     imagemagick_binary_path = r"C:\Program Files\ImageMagick-7.1.2-Q16-HDRI\magick.exe"
# else:  # For Linux/Mac
#     imagemagick_binary_path = "auto-detect"

# for live
IMAGEMAGICK_BINARY = os.getenv('IMAGEMAGICK_BINARY', '/usr/bin/convert')
mpconfig.change_settings({"IMAGEMAGICK_BINARY": IMAGEMAGICK_BINARY})


# Correctly call change_settings with a dictionary
# mpconfig.change_settings({"IMAGEMAGICK_BINARY": imagemagick_binary_path})


logger = logging.getLogger(__name__)


class AdvancedVideoProcessor:
    """
    Comprehensive video processor that adds subtitles, emojis, clipart, and creates short-form content
    Enhanced with AI-powered emoji selection and importance detection
    """

    def __init__(self, openai_api_key: str = None, temp_dir: str = None):
        self.openai_api_key = openai_api_key or os.getenv("OPENAI_API_KEY")
        self.temp_dir = temp_dir or tempfile.gettempdir()

        if self.openai_api_key:
            self.openai_client = openai.OpenAI(api_key=self.openai_api_key)
        else:
            self.openai_client = None

        self._init_nlp_models()
        self.emotion_analyzer = None

        self.emotion_emojis = {
            'joy': ['😊', '😄', '🎉', '✨', '💫'], 'sadness': ['😢', '😔', '💔', '😞'],
            'anger': ['😠', '😡', '🔥', '💢'], 'fear': ['😨', '😰', '😱', '🙈'],
            'surprise': ['😲', '😮', '🤯', '😯'], 'disgust': ['🤢', '😷', '🤮', '😖'],
            'love': ['❤️', '💕', '😍', '🥰', '💖']
        }

        self.keyword_emojis = {
            'money': '💰', 'win': '🏆', 'success': '🎉', 'happy': '😊', 'great': '👍',
            'love': '❤️', 'fire': '🔥', 'amazing': '🤩', 'wow': '😮', 'idea': '💡',
            'sad': '😢', 'cry': '😭', 'angry': '😠', 'cool': '😎', 'lol': '😂',
            'question': '❓', 'warning': '⚠️', 'check': '✅', 'wrong': '❌'
        }

        self.platform_settings = {
            'tiktok': {'aspect_ratio': (9, 16), 'max_duration': 60, 'resolution': (1080, 1920)},
            'instagram': {'aspect_ratio': (9, 16), 'max_duration': 90, 'resolution': (1080, 1920)},
            'youtube_shorts': {'aspect_ratio': (9, 16), 'max_duration': 60, 'resolution': (1080, 1920)},
            'twitter': {'aspect_ratio': (16, 9), 'max_duration': 140, 'resolution': (1280, 720)}
        }

        self.tiktok_styles = {

            'simple': {
                'fontsize': 75,
                'font': 'Arial-Bold',
                'color': '#000000',
                'inactive_color': '#999999',
                'position': ('center', 0.65),
                'max_words_per_line': 2,
                'text_transform': 'lower',
                'animation': 'simple',
                'bg_color': (240, 240, 240, 200),
                'bg_padding': 20,
                'bg_border_radius': 15,
            },
            'popline': {
                'fontsize': 90,
                'font': 'Impact',
                'color': '#FFFFFF',
                'stroke_color': "#000000",
                'stroke_width': 3,
                'position': ('center', 0.65),
                'max_words_per_line': 2,
                'text_transform': 'upper',
                'word_spacing': 8,
                'shadow_offset': (6, 6),
                'shadow_blur_radius': 4,
                'animation': 'popline',
                'highlight_color': '#00FF00',
                'line_height': 12,
                'line_y_offset': 10,
            },
            'karaoke_pop': {
                'fontsize': 90,
                'font': 'Impact',
                'color': '#FFFFFF',
                'stroke_color': "#000000",
                'stroke_width': 3,
                'position': ('center', 0.65),
                'max_words_per_line': 3,
                'text_transform': 'upper',
                'word_spacing': 20,
                'shadow_offset': (8, 8),
                'shadow_blur_radius': 5,
                'animation': 'karaoke',
                'highlight_color': '#00FF00',
                'highlight_scale': 1.1,
            },
            'clean_modern': {
                'fontsize': 75,
                'font': 'Impact', 'color': '#FFFFFF', 'stroke_color': "#000000",
                'stroke_width': 6,
                'position': ('center', 0.65),
                'max_words_per_line': 2,
                'text_transform': 'upper', 'animation': 'zoom_bounce', 'accent_color': "#FFFFFF",
                'line_spacing': -10,
                'word_spacing': 8,
                'kerning': 1,
                'shadow_offset': (5, 5),
                'shadow_blur_radius': 4
            },
            'viral_style': {
                'fontsize': 90,
                'font': 'Impact',
                'color': "#FFFFFF",
                'stroke_color': "#070707",
                'stroke_width': 3,
                'position': ('center', 0.6),
                'max_words_per_line': 2,
                'text_transform': 'upper',
                'animation': 'bounce_in',
                'accent_color': '#FFFFFF',
                'line_spacing': -20,
                'word_spacing': 5,
                'shadow_offset': (8, 8),
                'shadow_blur_radius': 5
            },
            'modern': {
                'fontsize': 70,
                'font': 'Impact', 'color': '#FFFFFF', 'stroke_color': "#000000",
                'stroke_width': 3,
                'position': ('center', 0.6),
                'max_words_per_line': 2,
                'text_transform': 'upper', 'animation': 'fade_slide', 'accent_color': "#DBDBDB",
                'line_spacing': -8,
                'word_spacing': 3,
                'kerning': 1,
                'shadow_offset': (6, 6),
                'shadow_blur_radius': 4
            },
            'glitch_zoom': {
                'fontsize': 100,
                'font': 'Impact',
                'color': "#01FA22",      # Bright yellow for the main text
                'position': ('center', 0.6),
                'max_words_per_line': 3,
                'text_transform': 'upper',
                'animation': 'glitch_zoom',
                'shadow_color': "#FFFFFF",
                'shadow_offset': (8, 8),
                'zoom_amount': 1.05,       # How much it zooms in (5%)
                # How fast the zoom pulse is (in seconds)
                'zoom_speed': 0.5,
                'stroke_color': '#000000',
                'stroke_width': 4,
            },
            'beasty': {
                'fontsize': 70,
                'font': 'KOMIKAX.ttf',
                'fallback_font': 'Impact',
                'color': '#FFFFFF',
                'stroke_color': "#000000",
                'stroke_width': 10,
                'position': ('center', 0.65),
                'max_words_per_line': 4,
                'text_transform': 'upper',
                'animation': 'beasty_pop',
                'highlight_colors': ['#FFFF00', '#00FF00', '#00FFFF'],
                'highlight_rule': 'emphasis',
                'glow_color': '#FFFFFF',
                'glow_spread': 8,               # <--- Controls the SOLID part of the glow
                'glow_blur_radius': 20,
                'rotation_angle': -1
            },
        }

    def get_emotion_analyzer(self):
        if self.emotion_analyzer is None:
            try:
                self.emotion_analyzer = pipeline(
                    "text-classification", model="j-hartmann/emotion-english-distilroberta-base")
            except Exception as e:
                logger.warning(f"Failed to load emotion analyzer: {e}")
                self.emotion_analyzer = False
        return self.emotion_analyzer if self.emotion_analyzer is not False else None

    def _init_nlp_models(self):
        try:
            nltk.download('punkt', quiet=True)
            nltk.download('averaged_perceptron_tagger', quiet=True)
            nltk.download('wordnet', quiet=True)
            nltk.download('stopwords', quiet=True)
            try:
                self.nlp = spacy.load("en_core_web_sm")
            except OSError:
                logger.warning(
                    "spaCy model not found. Install with: python -m spacy download en_core_web_sm")
                self.nlp = None
        except Exception as e:
            logger.error(f"Error initializing NLP models: {e}")
            self.nlp = None

    def extract_transcript_with_timing(self, video_path: str) -> List[Dict[str, Any]]:
        """
        Extracts a transcript with word-level timestamps from a video file using WhisperX.
        WhisperX provides more accurate timestamps through a secondary alignment step.
        """
        temp_audio_path = None
        try:
            # --- 1. Extract Audio from Video ---
            with VideoFileClip(video_path) as video_clip:
                logger.info("Extracting audio from video with MoviePy...")
                # Use the class's temp_dir for the temporary file
                temp_audio_path = os.path.join(
                    self.temp_dir, f"temp_audio_{Path(video_path).stem}.wav")
                video_clip.audio.write_audiofile(
                    temp_audio_path, codec='pcm_s16le')
                logger.info(
                    f"Audio extracted to temporary file: {temp_audio_path}")

            # --- 2. Transcribe and Align with WhisperX ---
            device = "cuda" if torch.cuda.is_available() else "cpu"
            batch_size = 16  # Reduce if you run out of GPU memory
            compute_type = "float16" if device == "cuda" else "int8"

            # 2a. Load Whisper model and transcribe
            logger.info("Loading WhisperX model (medium)...")
            model = whisperx.load_model(
                "medium", device, compute_type=compute_type)

            logger.info(f"Transcribing audio file: {temp_audio_path}")
            audio = whisperx.load_audio(temp_audio_path)
            result = model.transcribe(audio, batch_size=batch_size)

            # Clean up memory before loading the alignment model
            del model
            gc.collect()
            torch.cuda.empty_cache()

            # 2b. Load alignment model and align transcript
            logger.info("Loading alignment model...")
            model_a, metadata = whisperx.load_align_model(
                language_code=result["language"], device=device)

            logger.info("Aligning transcript for accurate word timestamps...")
            result = whisperx.align(result["segments"], model_a, metadata, audio, device,
                                    return_char_alignments=False)

            # Clean up alignment model and audio from memory
            del model_a, metadata, audio
            gc.collect()
            torch.cuda.empty_cache()

            # --- 3. Format Transcript Data ---
            transcript_data = []
            if result and 'segments' in result:
                for segment in result['segments']:
                    for word_info in segment.get('words', []):
                        # Use 'score' from whisperx as confidence, it's a measure of alignment quality
                        transcript_data.append({
                            'word': word_info['word'].strip(),
                            'start': round(word_info.get('start', 0), 3),
                            'end': round(word_info.get('end', 0), 3),
                            'confidence': round(word_info.get('score', 1.0), 3)
                        })

            if not transcript_data:
                logger.warning(
                    "WhisperX returned no words. The video might be silent.")

            # --- 4. Optionally, save transcript to a file (like original function) ---
            try:
                base_name = Path(video_path).stem
                output_filename = os.path.join(
                    self.temp_dir, f"transcript_{base_name}.json")
                with open(output_filename, 'w', encoding='utf-8') as f:
                    json.dump(transcript_data, f, indent=2, ensure_ascii=False)
                logger.info(
                    f"Successfully dumped transcript to file: {output_filename}")
            except Exception as e:
                logger.error(f"Failed to write transcript to file: {e}")

            # --- 5. Return the structured transcript data ---
            return transcript_data

        except Exception as e:
            logger.error(
                f"Error extracting transcript with WhisperX: {e}", exc_info=True)
            return []
        finally:
            # --- 6. Clean up temporary audio file ---
            if temp_audio_path and os.path.exists(temp_audio_path):
                try:
                    os.remove(temp_audio_path)
                    logger.info(f"Deleted temp audio file: {temp_audio_path}")
                except Exception as e:
                    logger.error(
                        f"Failed to delete temp audio file {temp_audio_path}: {e}")

    def analyze_content_with_ai(self, transcript_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        segments = self._group_words_into_segments(transcript_data)
        for segment in segments:
            if not segment['words']:
                continue
            segment_text = ' '.join([word['word']
                                    for word in segment['words']])
            try:
                ai_analysis = self._get_ai_content_analysis(segment_text)
                last_word = segment['words'][-1]
                if ai_analysis.get('emoji'):
                    last_word['emoji'] = ai_analysis['emoji']
                if ai_analysis.get('is_important', False):
                    for word_data in segment['words']:
                        word_data['is_important'] = True
            except Exception as e:
                logger.error(f"Error in AI analysis for segment: {e}")
        return transcript_data

    def _get_ai_content_analysis(self, text: str) -> Dict[str, Any]:
        if not self.openai_api_key or not self.openai_client:
            return {}
        try:
            prompt = f'Analyze this text for video subtitles: "{text}". Provide a JSON response with "emoji" (a single relevant emoji or null) and "is_important" (boolean). Return only valid JSON.'
            response = self.openai_client.chat.completions.create(
                model="gpt-3.5-turbo", messages=[{"role": "user", "content": prompt}], max_tokens=100, temperature=0.2
            )
            return json.loads(response.choices[0].message.content.strip())
        except Exception as e:
            logger.error(f"Error in OpenAI analysis: {e}")
            return {}

    def _group_words_into_segments(self, transcript_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        segments, current_segment = [], {
            'words': [], 'start': None, 'end': None}
        for word_data in transcript_data:
            if current_segment['start'] is None:
                current_segment['start'] = word_data['start']
            current_segment['words'].append(word_data)
            current_segment['end'] = word_data['end']
            if len(current_segment['words']) >= 15 or word_data['word'].endswith(('.', '!', '?')):
                if current_segment['words']:
                    segments.append(current_segment)
                current_segment = {'words': [], 'start': None, 'end': None}
        if current_segment['words']:
            segments.append(current_segment)
        return segments

    def _add_fallback_emojis(self, transcript_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        sentences = self._group_words_into_segments(transcript_data)
        emotion_analyzer = self.get_emotion_analyzer()
        for sentence_data in sentences:
            if not sentence_data['words']:
                continue
            last_word = sentence_data['words'][-1]
            if 'emoji' in last_word:
                continue
            sentence_text = ' '.join(word['word'].lower()
                                     for word in sentence_data['words'])
            emoji_added = False
            for keyword, emoji in self.keyword_emojis.items():
                if f' {keyword} ' in f' {sentence_text} ':
                    last_word['emoji'] = emoji
                    logger.info(
                        f"Added emoji '{emoji}' based on keyword '{keyword}'.")
                    emoji_added = True
                    break
            if emoji_added:
                continue
            if emotion_analyzer:
                try:
                    result = emotion_analyzer(sentence_text)[0]
                    if result['score'] > 0.5 and result['label'] in self.emotion_emojis:
                        emoji_choice = np.random.choice(
                            self.emotion_emojis[result['label']])
                        last_word['emoji'] = emoji_choice
                        logger.info(
                            f"Added emoji '{emoji_choice}' from ML (Emotion: {result['label']}, Conf: {result['score']:.2f}).")
                except Exception as e:
                    logger.warning(f"ML emotion analysis failed: {e}")
        return transcript_data

    def create_emoji_overlay_clips(self, transcript_data: List[Dict[str, Any]], video_size: Tuple[int, int]) -> List:
        """
        Creates full-color emoji overlays by first rendering them to a transparent PNG image,
        then using ImageClip. This bypasses TextClip's color limitations.
        """
        emoji_clips = []
        emoji_y_position = 0.48

        # Load an emoji-supporting font for Pillow
        try:
            # On Windows, this font is usually available.
            font_path = "C:/Windows/Fonts/seguiemj.ttf"
            if not os.path.exists(font_path):
                # Fallback for other systems or if font is missing
                font_path = "Arial.ttf"
            emoji_font = ImageFont.truetype(font_path, size=80)
        except IOError:
            logger.warning(
                "Segoe UI Emoji font not found. Emoji rendering might be black and white.")
            emoji_font = ImageFont.load_default()

        for i, word_data in enumerate(transcript_data):
            if word_data.get('emoji') and word_data.get('start') is not None:
                try:
                    emoji_char = word_data['emoji']

                    # --- THIS IS THE FIX ---
                    temp_emoji_path = os.path.join(
                        self.temp_dir, f"temp_emoji_{i}.png")

                    bbox = emoji_font.getbbox(emoji_char)
                    image_width = bbox[2] - bbox[0]
                    image_height = bbox[3] - bbox[1]

                    padding = 10
                    img = Image.new(
                        'RGBA', (image_width + padding*2, image_height + padding*2), (255, 255, 255, 0))
                    draw = ImageDraw.Draw(img)

                    draw.text((padding - bbox[0], padding - bbox[1]), emoji_char,
                              font=emoji_font, embedded_color=True, fill=(0, 0, 0, 255))

                    img.save(temp_emoji_path)

                    emoji_clip = ImageClip(
                        temp_emoji_path, duration=1.5).set_start(word_data['start'])

                    emoji_clip = emoji_clip.set_position(
                        ('center', emoji_y_position), relative=True)
                    emoji_clip = self._animate_emoji(emoji_clip)
                    emoji_clips.append(emoji_clip)

                except Exception as e:
                    logger.error(
                        f"Error creating emoji overlay for '{word_data.get('emoji')}': {e}", exc_info=True)

        logger.info(f"Created {len(emoji_clips)} emoji overlay clips.")
        return emoji_clips

    def _animate_emoji(self, emoji_clip):
        try:
            def size_func(t): return 0.5 + 0.5 * (t / 0.3) + \
                0.2 * np.sin(10 * t) if t < 0.3 else 1.0
            emoji_clip = emoji_clip.resize(
                size_func).crossfadein(0.3).crossfadeout(0.5)
            return emoji_clip
        except Exception as e:
            logger.error(f"Error animating emoji: {e}")
            return emoji_clip.crossfadein(0.2).crossfadeout(0.3)

    def _format_text_with_proper_spacing(self, text: str, style_config: Dict[str, Any]) -> str:
        transform = style_config.get('text_transform', 'none')
        if transform == 'upper':
            text = text.upper()
        elif transform == 'title':
            text = text.title()
        elif transform == 'lower':
            text = text.lower()
        words, lines, current_line = text.strip().split(), [], []
        max_words = style_config.get('max_words_per_line', 4)
        for word in words:
            current_line.append(word)
            if len(current_line) >= max_words:
                lines.append(self._add_word_spacing(
                    current_line, style_config))
                current_line = []
        if current_line:
            lines.append(self._add_word_spacing(current_line, style_config))
        return '\n'.join(lines)

    def _add_word_spacing(self, words: List[str], style_config: Dict[str, Any]) -> str:
        word_spacing = style_config.get('word_spacing', 8)
        if word_spacing >= 10:
            spacer = '   '
        elif word_spacing >= 6:
            spacer = '  '
        else:
            spacer = ' '
        return spacer.join(words)

    def _apply_tiktok_animation(self, clip, animation_type: str):
        try:
            if animation_type == 'bounce_in':
                clip = clip.crossfadein(0.2)

                def bounce_func(t): return 1 + 0.15 * \
                    np.exp(-4*t) * np.sin(12*t) if t < 0.6 else 1
                clip = clip.resize(bounce_func)
            else:
                clip = clip.crossfadein(0.2).crossfadeout(0.2)
            return clip
        except Exception as e:
            logger.error(f"Error applying animation {animation_type}: {e}")
            return clip.crossfadein(0.2).crossfadeout(0.2)

    def _create_simple_subtitle_clips(self, transcript_data: List[Dict[str, Any]],
                                      style_config: Dict[str, Any],
                                      video_size: Tuple[int, int]) -> List[CompositeVideoClip]:
        """
        Creates a "deep dive" effect with a rounded background, where the active
        word is highlighted by changing color/opacity.
        """
        from moviepy.editor import VideoClip

        all_clips = []
        subtitle_chunks = self._create_subtitle_chunks(
            transcript_data, style_config)
        dynamic_style = self._get_dynamic_style(style_config, video_size)

        try:
            font_path = self._find_font(dynamic_style['font'])
            logger.info(
                f"Simple Style: Attempting to use font at path: {font_path}")
            base_font = ImageFont.truetype(
                font_path, dynamic_style['fontsize'])
        except IOError:
            logger.warning(
                f"Font '{dynamic_style['font']}' not found. Falling back to default.")
            base_font = ImageFont.load_default()

        def create_make_frame_function(chunk, word_layout_list, text_height, bg_box):
            def make_frame(t):
                pil_image = Image.new('RGBA', video_size, (0, 0, 0, 0))
                draw = ImageDraw.Draw(pil_image)
                global_time = chunk['start'] + t

                # 1. Draw the rounded rectangle background
                bg_color = dynamic_style.get('bg_color', (240, 240, 240, 200))
                radius = dynamic_style.get('bg_border_radius', 15)
                draw.rounded_rectangle(bg_box, radius=radius, fill=bg_color)

                # 2. Draw each word with the correct color
                for word_details in word_layout_list:
                    is_active = (global_time >= word_details['start_time']) and (
                        global_time < word_details['end_time'])

                    color = dynamic_style['color'] if is_active else dynamic_style['inactive_color']

                    draw.text((word_details['x'], word_details['y']),
                              word_details['text'], font=base_font, fill=color)

                alpha = np.array(pil_image.getchannel('A'))
                rgb = np.array(pil_image.convert('RGB'))
                return rgb, alpha
            return make_frame

        for chunk in subtitle_chunks:
            if not chunk['word_data']:
                continue

            # --- PRE-CALCULATION ---
            transform = dynamic_style.get('text_transform', 'none')
            words_in_chunk = []
            for w in chunk['word_data']:
                word = w['word']
                if transform == 'upper':
                    word = word.upper()
                elif transform == 'lower':
                    word = word.lower()
                elif transform == 'title':
                    word = word.title()
                words_in_chunk.append(word)

            line_text = self._add_word_spacing(words_in_chunk, dynamic_style)

            temp_img = Image.new('RGBA', (1, 1))
            draw_calc = ImageDraw.Draw(temp_img)
            bbox = draw_calc.textbbox((0, 0), line_text, font=base_font)
            text_width, text_height = bbox[2] - bbox[0], bbox[3] - bbox[1]

            y_pos_factor = dynamic_style['position'][1]
            start_x = (video_size[0] - text_width) / 2
            start_y = (video_size[1] * y_pos_factor) - (text_height / 2)

            padding = dynamic_style.get('bg_padding', 20)
            bg_box = [start_x - padding, start_y - padding, start_x +
                      text_width + padding, start_y + text_height + padding]

            word_layout_list = []
            current_x = start_x
            for i, word_info in enumerate(chunk['word_data']):
                word_text = words_in_chunk[i]
                word_width_px = base_font.getlength(word_text)

                word_layout_list.append({
                    'text': word_text, 'x': current_x, 'y': start_y,
                    'start_time': word_info['start'], 'end_time': word_info['end']
                })
                spacer_width = base_font.getlength(
                    self._add_word_spacing(['', ''], dynamic_style))
                current_x += word_width_px + spacer_width

            make_frame_for_this_chunk = create_make_frame_function(
                chunk, word_layout_list, text_height, bg_box)

            subtitle_clip = VideoClip(make_frame=lambda t, fn=make_frame_for_this_chunk: fn(t)[
                                      0], ismask=False, duration=chunk['duration'])
            alpha_mask = VideoClip(make_frame=lambda t, fn=make_frame_for_this_chunk: fn(t)[
                                   1]/255.0, ismask=True, duration=chunk['duration'])

            subtitle_clip = subtitle_clip.set_mask(
                alpha_mask).set_start(chunk['start'])
            logger.info(
                f"Deep Dive clip created for text: '{chunk['text']}' | Start: {chunk['start']:.2f}s")
            all_clips.append(subtitle_clip)

        return all_clips

    def _create_popline_subtitle_clips(self, transcript_data: List[Dict[str, Any]],
                                       style_config: Dict[str, Any],
                                       video_size: Tuple[int, int]) -> List[CompositeVideoClip]:
        """
        Creates a combined popline/karaoke effect with stable positioning.
        """
        from moviepy.editor import VideoClip

        all_clips = []
        subtitle_chunks = self._create_subtitle_chunks(
            transcript_data, style_config)
        dynamic_style = self._get_dynamic_style(style_config, video_size)

        try:
            font_path = self._find_font(dynamic_style['font'])
            base_font = ImageFont.truetype(
                font_path, dynamic_style['fontsize'])
            highlight_font_size = int(
                dynamic_style['fontsize'] * dynamic_style.get('highlight_scale', 1.1))
            highlight_font = ImageFont.truetype(font_path, highlight_font_size)
        except IOError:
            logger.warning(
                f"Font '{dynamic_style['font']}' not found. Falling back to default.")
            base_font = ImageFont.load_default()
            highlight_font_size = int(dynamic_style['fontsize'] * 1.1)
            highlight_font = ImageFont.load_default()

        def create_make_frame_function(chunk, word_layout_list):
            def make_frame(t):
                pil_image = Image.new('RGBA', video_size, (0, 0, 0, 0))
                draw = ImageDraw.Draw(pil_image)
                global_time = chunk['start'] + t

                for word_details in word_layout_list:
                    is_active = (global_time >= word_details['start_time']) and (
                        global_time < word_details['end_time'])

                    font = highlight_font if is_active else base_font
                    x_pos = word_details['x_centered']
                    y_pos = word_details['y_centered']

                    draw.text((x_pos, y_pos), word_details['text'], font=font, fill=dynamic_style['color'],
                              stroke_width=dynamic_style['stroke_width'], stroke_fill=dynamic_style['stroke_color'])

                    if is_active:
                        line_height = dynamic_style.get('line_height', 10)
                        line_color = dynamic_style.get(
                            'highlight_color', '#8A2BE2')
                        y_offset = dynamic_style.get('line_y_offset', 5)

                        line_y1 = word_details['y_centered'] + \
                            word_details['h_highlight'] + y_offset
                        line_y2 = line_y1 + line_height

                        anim_duration = 0.2
                        time_into_word = global_time - \
                            word_details['start_time']
                        progress = min(1.0, time_into_word / anim_duration)
                        animated_width = word_details['w_highlight'] * progress

                        # Underline is drawn centered within the slot
                        line_start_x = word_details['slot_x'] + \
                            (word_details['slot_width'] - animated_width) / 2
                        draw.rectangle(
                            [line_start_x, line_y1, line_start_x + animated_width, line_y2], fill=line_color)

                alpha = np.array(pil_image.getchannel('A'))
                rgb = np.array(pil_image.convert('RGB'))
                return rgb, alpha
            return make_frame

        for chunk in subtitle_chunks:
            if not chunk['word_data']:
                continue

            words_in_chunk = [w['word'].upper() for w in chunk['word_data']]

            # Calculate total width based on the largest size of each word
            total_width = 0
            word_widths = [highlight_font.getlength(w) for w in words_in_chunk]
            spacer_width = highlight_font.getlength(
                self._add_word_spacing(['', ''], dynamic_style))
            total_width = sum(word_widths) + \
                (spacer_width * (len(words_in_chunk) - 1))

            start_x = (video_size[0] - total_width) / 2

            word_layout_list = []
            current_x = start_x

            for i, word_info in enumerate(chunk['word_data']):
                word_text = words_in_chunk[i]
                slot_width = word_widths[i]

                # Get metrics for both sizes
                w_normal = base_font.getlength(word_text)
                h_normal = base_font.getbbox(
                    word_text)[3] - base_font.getbbox(word_text)[1]
                w_highlight, h_highlight = word_widths[i], highlight_font.getbbox(
                    word_text)[3] - highlight_font.getbbox(word_text)[1]

                y_pos_factor = dynamic_style['position'][1]

                word_layout_list.append({
                    'text': word_text,
                    'slot_x': current_x, 'slot_width': slot_width,
                    # Centered X for normal text
                    'x_centered': current_x + (slot_width - w_normal) / 2,
                    # Y position based on tallest word
                    'y_centered': (video_size[1] * y_pos_factor) - (h_highlight / 2),
                    'w_highlight': w_highlight, 'h_highlight': h_highlight,
                    'start_time': word_info['start'], 'end_time': word_info['end']
                })

                current_x += slot_width + spacer_width

            make_frame_for_this_chunk = create_make_frame_function(
                chunk, word_layout_list)

            subtitle_clip = VideoClip(make_frame=lambda t, fn=make_frame_for_this_chunk: fn(t)[
                                      0], ismask=False, duration=chunk['duration'])
            alpha_mask = VideoClip(make_frame=lambda t, fn=make_frame_for_this_chunk: fn(t)[
                                   1]/255.0, ismask=True, duration=chunk['duration'])

            subtitle_clip = subtitle_clip.set_mask(
                alpha_mask).set_start(chunk['start'])
            logger.info(
                f"Popline clip created for text: '{chunk['text']}' | Start: {chunk['start']:.2f}s")
            all_clips.append(subtitle_clip)

        return all_clips

    def _calculate_text_y_pos(self, video_size, style_config, text):
        """A helper to calculate the Y position for drawing text with Pillow."""
        # This is a simplified calculation. For perfect centering, more complex logic is needed,
        # but this is robust for single lines.
        font_path = self._find_font(style_config['font'])
        font = ImageFont.truetype(font_path, style_config['fontsize'])

        # Use TextClip to get an approximate height, as it's good at that
        temp_clip = TextClip(
            txt="A", fontsize=style_config['fontsize'], font=font_path)
        text_height = temp_clip.h

        y_pos_factor = style_config['position'][1]

        return (video_size[1] * y_pos_factor) - (text_height / 2)

    def _create_single_color_text(self, text: str, style_config: Dict[str, Any],
                                  start_time: float, duration: float, video_size: Tuple[int, int]):
        """
        Creates a text clip with a soft drop shadow, using dynamically scaled styles.
        """
        from PIL import Image, ImageFilter

        # --- THIS IS THE KEY CHANGE ---
        # Get a resolution-aware style configuration first
        dynamic_style = self._get_dynamic_style(style_config, video_size)
        # --- END OF KEY CHANGE ---

        logger.info(
            f"Creating subtitle text clip for video with size (WxH): {video_size[0]}x{video_size[1]}")

        formatted_text = text if '  ' in text else self._format_text_with_proper_spacing(
            text, dynamic_style)

        logger.info(f"dynamic_style['font']: {dynamic_style['font']}")

        # Use the new dynamic_style dictionary for all parameters
        shadow_offset = dynamic_style.get('shadow_offset', (3, 3))
        blur_radius = dynamic_style.get('shadow_blur_radius', 2)

        font_path = self._find_font(dynamic_style['font'])
        logger.info(f"Using font for TextClip: {font_path}")

        text_params = {
            "txt": formatted_text,
            "fontsize": dynamic_style['fontsize'],
            "font": font_path,
            "method": 'caption',
            "size": (int(video_size[0] * 0.9), None),
            "align": 'center',
            "interline": dynamic_style.get('line_spacing', -5)
        }

        shadow_clip_raw = TextClip(color='black', **text_params)

        shadow_mask_np = shadow_clip_raw.get_frame(0)[:, :, 0] > 0
        shadow_pil = Image.fromarray(shadow_mask_np.astype('uint8') * 255)
        shadow_pil_blurred = shadow_pil.filter(
            ImageFilter.GaussianBlur(radius=blur_radius))

        blurred_mask_clip = ImageClip(
            np.array(shadow_pil_blurred), ismask=True)

        shadow_color_clip = ColorClip(
            size=shadow_clip_raw.size, color=(0, 0, 0)).set_opacity(0.5)
        shadow_clip = shadow_color_clip.set_mask(blurred_mask_clip)

        text_clip = TextClip(
            color=dynamic_style['color'],
            stroke_color=dynamic_style['stroke_color'],
            stroke_width=dynamic_style['stroke_width'],
            **text_params
        )

        final_clip = CompositeVideoClip([
            shadow_clip.set_position(shadow_offset),
            text_clip.set_position((0, 0))
        ], size=shadow_clip.size)

        # Position is based on percentage, so it doesn't need scaling
        position = dynamic_style['position']
        if isinstance(position[1], float):
            y_pos = int(video_size[1] * position[1])
            final_clip = final_clip.set_position(('center', y_pos))
        else:
            final_clip = final_clip.set_position(position)

        return final_clip.set_start(start_time).set_duration(duration)

    def _create_subtitle_canvass(self, text_layout: Dict[str, Any], style_config: Dict[str, Any], fixed_width: Optional[int] = None, fixed_height: Optional[int] = None) -> Tuple[Image.Image, int, int]:
        """
        Creates a perfectly padded canvas for a given text layout to prevent clipping.
        [CORRECTED] Now correctly accounts for shadow_offset to prevent vertical clipping.
        """
        stroke_width = style_config.get('stroke_width', 0)
        glow_radius = style_config.get('glow_blur_radius', 0)

        # Get the shadow offset to include it in size calculations.
        shadow_offset = style_config.get('shadow_offset', (0, 0))

        # Padding now accounts for stroke, glow, AND the horizontal shadow offset
        padding_x = stroke_width + glow_radius + abs(shadow_offset[0]) + 5
        padding_y = stroke_width + glow_radius + 5

        # Determine canvas dimensions
        canvas_w = fixed_width if fixed_width else int(
            text_layout['max_line_width'] + padding_x * 2)

        if fixed_height:
            canvas_h = fixed_height
        else:

            # The new height is: Text Height + Top/Bottom Padding + Downward Shadow Offset
            canvas_h = int(text_layout['total_height'] +
                           padding_y * 2 + abs(shadow_offset[1]))
            # -----------------------

        canvas = Image.new('RGBA', (canvas_w, canvas_h), (0, 0, 0, 0))
        return canvas, padding_x, padding_y

    def _create_glitch_zoom_subtitle_clips(self, transcript_data: List[Dict[str, Any]],
                                           style_config: Dict[str, Any],
                                           video_size: Tuple[int, int]) -> List[CompositeVideoClip]:
        """
        Creates subtitles with a "glitch" color echo and a continuous zoom pulse animation,
        using a robust Pillow canvas for rendering.
        """
        import numpy as np
        import math
        from PIL import Image, ImageDraw

        all_clips = []
        subtitle_chunks = self._create_subtitle_chunks(
            transcript_data, style_config)
        dynamic_style = self._get_dynamic_style(style_config, video_size)

        try:
            font_path = self._find_font(dynamic_style['font'])
            font = ImageFont.truetype(font_path, dynamic_style['fontsize'])
        except IOError:
            logger.error(
                f"FATAL: Font '{dynamic_style['font']}' not found. Falling back.")
            try:
                # Fallback to a common system font if the primary one fails
                font = ImageFont.truetype(
                    "impact.ttf", dynamic_style['fontsize'])
            except IOError:
                logger.error(
                    "Fallback font also failed. Please check font configuration.")
                return []

        # Define max width for text wrapping
        max_text_width_px = int(video_size[0] * 0.95)

        for chunk in subtitle_chunks:
            if not chunk['word_data']:
                continue

            text_to_render = self._format_text_with_proper_spacing(
                chunk['text'], dynamic_style)

            # --- 1. LAYOUT AND CANVAS SETUP ---
            # Calculate the layout for potentially multi-line text
            layout = self._layout_text_with_wrapping(
                text_to_render, font, max_text_width_px)
            if not layout["lines"]:
                continue

            # Create a canvas with enough padding for strokes and shadows
            canvas, padding_x, padding_y = self._create_subtitle_canvass(
                layout, dynamic_style)
            draw = ImageDraw.Draw(canvas)

            # Center the entire text block vertically on the canvas
            block_start_y = (canvas.height - layout['total_height']) / 2

            shadow_offset = dynamic_style.get('shadow_offset', (8, 8))
            stroke_width = dynamic_style.get('stroke_width', 4)
            stroke_color = dynamic_style.get('stroke_color', '#000000')

            # --- 2. RENDER TEXT LAYERS (GLITCH EFFECT) ---
            # Loop through each line of wrapped text
            for i, line_text in enumerate(layout["lines"]):
                # Center the line horizontally
                line_x = (canvas.width - layout["line_widths"][i]) / 2
                line_y = block_start_y + (i * layout["line_height"])

                # A) Draw the shadow/echo layer first
                draw.text(
                    (line_x + shadow_offset[0], line_y + shadow_offset[1]),
                    line_text,
                    font=font,
                    fill=dynamic_style.get('shadow_color', '#FFFFFF'),
                    stroke_width=stroke_width,
                    stroke_fill=stroke_color
                )

                # B) Draw the main text layer on top
                draw.text(
                    (line_x, line_y),
                    line_text,
                    font=font,
                    fill=dynamic_style.get('color', '#01FA22'),
                    stroke_width=stroke_width,
                    stroke_fill=stroke_color
                )

            # --- 3. ANIMATE AND POSITION ---
            # Convert the Pillow Image to a MoviePy ImageClip
            base_clip = ImageClip(np.array(canvas), duration=chunk['duration'])

            # Define the zoom animation function (copied from your original)
            zoom_amount = dynamic_style.get('zoom_amount', 1.05)
            zoom_speed = dynamic_style.get('zoom_speed', 0.5)

            def zoom_func(t):
                return 1 + (zoom_amount - 1) * abs(np.sin(t * np.pi / zoom_speed))

            # Apply the zoom animation
            animated_clip = base_clip.fx(vfx.resize, zoom_func)

            # Position the final animated clip on the full video screen
            final_y_pos = (
                video_size[1] * dynamic_style['position'][1]) - (animated_clip.h / 2)
            final_clip = CompositeVideoClip(
                [animated_clip.set_position(('center', final_y_pos))],
                size=video_size
            ).set_start(chunk['start']).set_duration(chunk['duration'])

            all_clips.append(final_clip)

        return all_clips

    def create_ai_enhanced_subtitles(self, video_path: str, transcript_data: List[Dict[str, Any]],
                                     output_path: str, style: str = "clean_modern",
                                     enable_emoji_overlays: bool = True,
                                     enable_importance_highlighting: bool = True) -> str:
        import subprocess
        import os
        from moviepy.video.io.ffmpeg_writer import ffmpeg_write_video

        # Initialize variables for the finally block
        video = None
        final_visuals = None
        temp_video_path = None
        try:
            # Step 1: Load the original video to get its properties
            video = VideoFileClip(video_path, fps_source='fps')
            if not video:
                raise ValueError("MoviePy could not open video file.")

            # Ensure we have a valid, integer FPS
            output_fps = int(round(video.fps if video.fps else 24))
            logger.info(
                f"Using rounded integer FPS: {output_fps} for final render.")

            # Step 2: Create a list of all visual clips
            # The base layer is the original video *without its audio*
            clips_to_composite = [video.without_audio()]
            style_config = self.tiktok_styles.get(
                style, self.tiktok_styles['clean_modern'])

            if enable_emoji_overlays:
                emoji_clips = self.create_emoji_overlay_clips(
                    transcript_data, video.size)
                if emoji_clips:
                    clips_to_composite.extend(emoji_clips)

            # Add subtitle clips based on the selected style
            animation_type = style_config.get('animation', 'fade_slide')
            if animation_type == 'karaoke':
                logger.info("Creating Karaoke-style word-by-word subtitles.")
                subtitle_clips = self._create_karaoke_subtitle_clips(
                    transcript_data, style_config, video.size)
                clips_to_composite.extend(subtitle_clips)
            elif animation_type == 'popline':
                logger.info("Creating Popline-style word-by-word subtitles.")
                subtitle_clips = self._create_popline_subtitle_clips(
                    transcript_data, style_config, video.size)
                clips_to_composite.extend(subtitle_clips)
            elif animation_type == 'simple':
                logger.info("Creating Simple-style word-by-word subtitles.")
                subtitle_clips = self._create_simple_subtitle_clips(
                    transcript_data, style_config, video.size)
                clips_to_composite.extend(subtitle_clips)
            elif animation_type == 'glitch_zoom':
                logger.info("Creating Glitch-Zoom-style subtitles.")
                subtitle_clips = self._create_glitch_zoom_subtitle_clips(
                    transcript_data, style_config, video.size)
                clips_to_composite.extend(subtitle_clips)
            elif animation_type == 'beasty_pop':
                logger.info("Creating Beasty-style word-by-word subtitles.")
                subtitle_clips = self._create_beasty_subtitle_clips(
                    transcript_data, style_config, video.size)
                clips_to_composite.extend(subtitle_clips)

            else:
                logger.info("Creating standard chunk-based subtitles.")
                subtitle_chunks = self._create_subtitle_chunks(
                    transcript_data, style_config)
                for chunk in subtitle_chunks:
                    txt_clip = self._create_single_color_text(
                        chunk['text'], style_config, chunk['start'], chunk['duration'], video.size)
                    animated_clip = self._apply_tiktok_animation(
                        txt_clip, style_config['animation'])
                    clips_to_composite.append(animated_clip)

            # Step 3: Composite all visual elements and explicitly set FPS
            final_visuals = CompositeVideoClip(
                clips_to_composite, size=video.size).set_fps(output_fps)
            final_visuals.duration = video.duration  # Also explicitly set duration

            # Step 4: Write the visuals to a temporary, silent video file
            temp_video_path = os.path.join(
                self.temp_dir, f"temp_silent_{Path(output_path).name}")

            # Use ffmpeg_write_video directly as in your example for maximum stability
            ffmpeg_write_video(
                final_visuals,
                temp_video_path,
                fps=output_fps,
                codec='libx264',
                threads=os.cpu_count(),
                preset='fast',  # previously 'medium'
            )

            # Important: Close clips to release file handles before the subprocess call
            video.close()
            final_visuals.close()

            # Step 5: Use FFmpeg subprocess to combine the new visuals with the original audio
            logger.info(
                f"Merging silent video ({temp_video_path}) with audio from {video_path}")
            ffmpeg_command = [
                'ffmpeg', '-y',
                '-i', temp_video_path,      # Input 0: New silent video
                # Input 1: Original video (for audio)
                '-i', video_path,
                '-c:v', 'copy',             # Copy video stream without re-encoding
                '-c:a', 'aac',              # Encode audio to AAC
                '-map', '0:v:0',            # Use video from input 0
                '-map', '1:a:0?',           # Use audio from input 1 (optional)
                '-shortest',                # Finish when the shortest input ends
                output_path
            ]

            subprocess.run(ffmpeg_command, check=True,
                           stdout=subprocess.PIPE, stderr=subprocess.PIPE)

            logger.info(f"Successfully created final video at: {output_path}")
            return output_path

        except Exception as e:
            if isinstance(e, subprocess.CalledProcessError):
                logger.error(
                    f"FFmpeg command failed. Stderr: {e.stderr.decode()}")
            else:
                logger.error(
                    f"FATAL error in create_ai_enhanced_subtitles: {e}", exc_info=True)
            raise
        finally:
            # Final robust cleanup
            if video:
                try:
                    video.close()
                except Exception:
                    pass
            if final_visuals:
                try:
                    final_visuals.close()
                except Exception:
                    pass
            if temp_video_path and os.path.exists(temp_video_path):
                try:
                    os.remove(temp_video_path)
                except Exception as e:
                    logger.error(
                        f"Failed to clean up temp file {temp_video_path}: {e}")

    def _create_single_word_clip(self, word_text: str, style_config: Dict[str, Any], fontsize: int) -> CompositeVideoClip:
        """Helper to create a styled CompositeVideoClip for a single word."""
        from PIL import Image, ImageFilter

        shadow_offset = style_config.get('shadow_offset', (3, 3))
        blur_radius = style_config.get('shadow_blur_radius', 2)

        # Use the 'fontsize' passed directly into the function.
        text_params = {
            "txt": word_text,
            "fontsize": fontsize,
            "font": style_config['font'],
            "method": 'caption',
            "align": 'center'
        }

        shadow_raw = TextClip(color='black', **text_params)
        boolean_mask = shadow_raw.get_frame(0)[:, :, 0] > 0
        shadow_pil = Image.fromarray(
            boolean_mask.astype('uint8') * 255, mode='L')
        shadow_pil_blurred = shadow_pil.filter(
            ImageFilter.GaussianBlur(radius=blur_radius))

        blurred_mask_clip = ImageClip(
            np.array(shadow_pil_blurred), ismask=True)
        shadow_color_clip = ColorClip(
            size=shadow_raw.size, color=(0, 0, 0)).set_opacity(0.6)
        shadow_clip = shadow_color_clip.set_mask(blurred_mask_clip)

        text_clip = TextClip(
            color=style_config['color'],
            stroke_color=style_config.get('stroke_color'),
            stroke_width=style_config.get('stroke_width'),
            **text_params
        )

        return CompositeVideoClip(
            [shadow_clip.set_position(shadow_offset),
             text_clip.set_position((0, 0))],
            size=(text_clip.w + shadow_offset[0] + 5,
                  text_clip.h + shadow_offset[1] + 5)
        ).set_duration(999)

    def _find_font(self, font_name: str) -> str:
        """
        Robustly finds a font file path. It prioritizes a local 'fonts'
        sub-directory, then checks if the name is an absolute path, and finally
        searches system directories as a fallback.
        """
        # If the font name already has an extension, use it directly. Otherwise, add .ttf.
        if not font_name.lower().endswith(('.ttf', '.otf', '.ttc')):
            font_file_name = font_name + '.ttf'
        else:
            font_file_name = font_name

        try:
            # Get the directory where this script file is located.
            script_dir = os.path.dirname(os.path.abspath(__file__))
            local_font_path = os.path.join(script_dir, 'fonts', font_file_name)
            if os.path.exists(local_font_path):
                logger.info(
                    f"Found font in local 'fonts' folder: {local_font_path}")
                return local_font_path
        except Exception as e:
            logger.warning(f"Could not check local fonts folder: {e}")

        # 2. Check if the provided name is already a full path that exists.
        if os.path.exists(font_name):
            return font_name

        # 3. Fallback to searching system font directories.
        if os.name == 'nt':  # For Windows
            font_dir = "C:/Windows/Fonts"
            font_path = os.path.join(font_dir, font_file_name)
            if os.path.exists(font_path):
                return font_path
        else:  # For Linux/macOS
            font_dirs = ['/usr/share/fonts/truetype/', '/usr/share/fonts/opentype/',
                         '/System/Library/Fonts/', '/Library/Fonts/', os.path.expanduser('~/.fonts')]
            for d in font_dirs:
                if not os.path.isdir(d):
                    continue
                for root, _, files in os.walk(d):
                    for f in files:
                        # Case-insensitive check for the font file
                        if font_file_name.lower() == f.lower():
                            return os.path.join(root, f)

        # 4. If all else fails, return the original name and let MoviePy try to find it.
        logger.warning(
            f"Could not find '{font_name}' in local or system paths. Returning original name.")
        return font_name

    def _find_bounding_box(self, image_array: np.ndarray) -> Tuple[int, int, int, int]:
        """Finds the bounding box of non-transparent pixels in a NumPy array."""
        alpha = image_array[:, :, 3]
        y_indices, x_indices = np.where(alpha > 0)
        if y_indices.size == 0:  # Handle empty images
            return 0, 0, 0, 0
        min_x, max_x = np.min(x_indices), np.max(x_indices)
        min_y, max_y = np.min(y_indices), np.max(y_indices)
        return min_x, min_y, max_x + 1, max_y + 1

    def _create_subtitle_canvas(self, text_layout: Dict[str, Any], style_config: Dict[str, Any], fixed_width: Optional[int] = None, fixed_height: Optional[int] = None) -> Tuple[Image.Image, int, int]:
        """
        Creates a perfectly padded canvas for a given text layout to prevent clipping.
        Now supports fixed width and height.
        """
        stroke_width = style_config.get('stroke_width', 0)
        glow_radius = style_config.get('glow_blur_radius', 0)

        padding_x = stroke_width + glow_radius + 5
        padding_y = stroke_width + glow_radius + 5

        # Determine canvas dimensions
        canvas_w = fixed_width if fixed_width else int(
            text_layout['max_line_width'] + padding_x * 2)
        canvas_h = fixed_height if fixed_height else int(
            text_layout['total_height'] + padding_y * 2)

        canvas = Image.new('RGBA', (canvas_w, canvas_h), (0, 0, 0, 0))
        return canvas, padding_x, padding_y

    def _layout_text_with_wrapping(self, text: str, font: ImageFont.FreeTypeFont, max_width: int) -> Dict[str, Any]:
        """
        Calculates the layout for a given text to fit within a max_width.
        """
        words = text.split()
        if not words:
            return {"lines": [], "line_height": 0, "total_height": 0, "line_widths": [], "max_line_width": 0}

        # Use getbbox for more accurate line height
        try:
            _, top, _, bottom = font.getbbox("A_g")
            line_height = bottom - top
        except AttributeError:  # Fallback for older Pillow versions
            line_height = font.getsize("A")[1]

        lines = []
        line_widths = []
        current_line = []
        for word in words:
            test_line_str = " ".join(current_line + [word])
            if font.getlength(test_line_str) <= max_width:
                current_line.append(word)
            else:
                lines.append(" ".join(current_line))
                line_widths.append(font.getlength(" ".join(current_line)))
                current_line = [word]
        if current_line:
            lines.append(" ".join(current_line))
            line_widths.append(font.getlength(" ".join(current_line)))

        return {
            "lines": lines, "line_height": line_height, "total_height": len(lines) * line_height,
            "line_widths": line_widths, "max_line_width": max(line_widths) if line_widths else 0
        }

    def _create_beasty_subtitle_clips(self, transcript_data: List[Dict[str, Any]],
                                      style_config: Dict[str, Any],
                                      video_size: Tuple[int, int]) -> List[CompositeVideoClip]:

        import numpy as np
        import math
        from moviepy.editor import ImageClip, CompositeVideoClip
        import moviepy.video.fx.all as vfx
        from PIL import Image, ImageDraw, ImageFilter

        all_clips = []
        subtitle_chunks = self._create_subtitle_chunks(
            transcript_data, style_config)
        dynamic_style = self._get_dynamic_style(style_config, video_size)

        try:
            script_dir = os.path.dirname(__file__)
            font_path = os.path.join(
                script_dir, 'fonts', dynamic_style['font'])
            font = ImageFont.truetype(font_path, dynamic_style['fontsize'])
        except IOError:
            logger.error(
                f"FATAL: Could not find font at '{font_path}'. Falling back.")
            font = ImageFont.truetype("impact.ttf", dynamic_style['fontsize'])

        # 1. Define the maximum width for the TEXT ITSELF.
        # 98% = 385px on a 393px canvas now 80% = 640px on a 800px canvas
        max_text_width_px = int(video_size[0] * 0.80)

        # 2. Calculate a DYNAMIC canvas size that ADDS the stroke width as padding.
        #    This is the crucial step that prevents drawing outside the canvas.
        stroke_width = dynamic_style.get('stroke_width', 10)
        padding = stroke_width + 5

        # The canvas is now the text width PLUS the padding on BOTH sides.
        canvas_width = max_text_width_px + (padding * 2)
        canvas_height = 500 + (padding * 2)

        for chunk in subtitle_chunks:
            if not chunk['word_data']:
                continue

            text_to_render = chunk['text'].upper() if dynamic_style.get(
                'text_transform') == 'upper' else chunk['text']
            layout = self._layout_text_with_wrapping(
                text_to_render, font, max_text_width_px)
            if not layout["lines"]:
                continue

            canvas = Image.new(
                'RGBA', (canvas_width, canvas_height), (0, 0, 0, 0))

            blur_canvas = Image.new('RGBA', canvas.size, (0, 0, 0, 0))
            draw_blur = ImageDraw.Draw(blur_canvas)
            halo_canvas = Image.new('RGBA', canvas.size, (0, 0, 0, 0))
            draw_halo = ImageDraw.Draw(halo_canvas)
            outline_canvas = Image.new('RGBA', canvas.size, (0, 0, 0, 0))
            draw_outline = ImageDraw.Draw(outline_canvas)
            fill_canvas = Image.new('RGBA', canvas.size, (0, 0, 0, 0))
            draw_fill = ImageDraw.Draw(fill_canvas)

            block_start_y = (canvas_height - layout['total_height']) / 2

            highlight_colors = dynamic_style.get(
                'highlight_colors', ['#FFFF00'])
            highlight_word_counter = 0

            for i, line_text in enumerate(layout["lines"]):
                # Position the text correctly INSIDE the padded area
                line_x = padding + (max_text_width_px -
                                    layout["line_widths"][i]) / 2
                line_y = block_start_y + (i * layout["line_height"])

                words_in_line = line_text.split()
                is_last_word = (
                    i == len(layout["lines"]) - 1) and (len(words_in_line) > 0)
                is_emphasis_word = is_last_word or (
                    len(words_in_line) > 0 and words_in_line[-1].isdigit())
                color = dynamic_style['color']
                if dynamic_style.get('highlight_rule') == 'emphasis' and is_emphasis_word:
                    color = highlight_colors[highlight_word_counter % len(
                        highlight_colors)]
                    highlight_word_counter += 1

                glow_color = dynamic_style.get('glow_color', '#FFFFFF')
                halo_thickness = int(dynamic_style.get('glow_spread', 8))
                draw_halo.text((line_x, line_y), line_text, font=font, fill=glow_color,
                               stroke_width=halo_thickness, stroke_fill=glow_color)
                draw_blur.text((line_x, line_y), line_text, font=font, fill=glow_color,
                               stroke_width=halo_thickness, stroke_fill=glow_color)

                outline_thickness = dynamic_style.get('stroke_width', 8)
                for angle in range(0, 360, 20):
                    x_off, y_off = outline_thickness * \
                        math.cos(math.radians(angle)), outline_thickness * \
                        math.sin(math.radians(angle))
                    draw_outline.text((line_x + x_off, line_y + y_off), line_text,
                                      font=font, fill=dynamic_style['stroke_color'])

                draw_fill.text((line_x, line_y), line_text,
                               font=font, fill=color)

            blurred_canvas = blur_canvas.filter(ImageFilter.GaussianBlur(
                radius=dynamic_style.get('glow_blur_radius', 20)))
            final_image = Image.alpha_composite(blurred_canvas, halo_canvas)
            final_image = Image.alpha_composite(final_image, outline_canvas)
            final_image = Image.alpha_composite(final_image, fill_canvas)

            base_clip = ImageClip(np.array(final_image),
                                  duration=chunk['duration'])

            def pop_animation(t):
                if t < 0.3:
                    # changed from 20 to 30
                    return 1.1 - 0.1 * math.cos(t * 20)
                return 1.0
            animated_clip = base_clip.resize(pop_animation)

            final_y_pos = (
                video_size[1] * dynamic_style['position'][1]) - (animated_clip.h / 2)
            final_clip = CompositeVideoClip(
                [animated_clip.set_position(('center', final_y_pos))], size=video_size)
            final_clip = final_clip.set_start(
                chunk['start']).set_duration(chunk['duration'])
            all_clips.append(final_clip)

        return all_clips

    def _create_karaoke_subtitle_clips(self, transcript_data: List[Dict[str, Any]],
                                       style_config: Dict[str, Any],
                                       video_size: Tuple[int, int]) -> List[CompositeVideoClip]:
        """
        [FINAL, MULTI-LINE VERSION]
        Creates a word-by-word karaoke effect that correctly handles multi-line subtitles
        by pre-calculating the layout for wrapped text.
        """
        all_clips = []
        # Use the NEW, intelligent chunking function to get correctly wrapped lines
        subtitle_chunks = self._create_subtitle_chunks(
            transcript_data, style_config)
        dynamic_style = self._get_dynamic_style(style_config, video_size)

        try:
            font_path = self._find_font(dynamic_style['font'])
            logger.info(
                f"Using font '{dynamic_style['font']}' from path: {font_path}")
            base_font = ImageFont.truetype(
                font_path, dynamic_style['fontsize'])
            highlight_font_size = int(
                dynamic_style['fontsize'] * dynamic_style.get('highlight_scale', 1.1))
            highlight_font = ImageFont.truetype(font_path, highlight_font_size)
        except IOError:
            logger.warning(
                f"Font '{dynamic_style['font']}' not found. Falling back to default.")
            base_font = ImageFont.load_default()
            highlight_font_size = int(dynamic_style['fontsize'] * 1.1)
            highlight_font = ImageFont.load_default()

        # Define the maximum width for the text, same as in the chunking function
        max_text_width_px = int(video_size[0] * 0.90)

        def create_make_frame_function(chunk, word_layout_list):
            def make_frame(t):
                pil_image = Image.new('RGBA', video_size, (0, 0, 0, 0))
                draw = ImageDraw.Draw(pil_image)
                global_time = chunk['start'] + t

                for word_details in word_layout_list:
                    is_active = (global_time >= word_details['start_time']) and (
                        global_time < word_details['end_time'])

                    if is_active:
                        font = highlight_font
                        color = dynamic_style.get('highlight_color', '#00FF00')
                        # Center the larger, active word within its calculated slot
                        x_pos = word_details['x'] + (
                            word_details['w_inactive'] - word_details['w_active']) / 2
                        y_pos = word_details['y'] + (
                            word_details['h_inactive'] - word_details['h_active']) / 2
                    else:
                        font = base_font
                        color = dynamic_style['color']
                        x_pos = word_details['x']
                        y_pos = word_details['y']

                    draw.text(
                        (x_pos, y_pos),
                        word_details['text'],
                        font=font,
                        fill=color,
                        stroke_width=dynamic_style.get('stroke_width', 2),
                        stroke_fill=dynamic_style.get(
                            'stroke_color', '#000000')
                    )

                frame_array = np.array(pil_image)
                rgb = frame_array[:, :, :3]
                alpha = frame_array[:, :, 3] / 255.0
                return rgb, alpha

            return make_frame

        for chunk in subtitle_chunks:
            if not chunk['word_data']:
                continue

            # --- KEY CHANGE: USE THE LAYOUT HELPER TO HANDLE MULTI-LINE ---
            full_text_of_chunk = ' '.join([w['word'].upper() if dynamic_style.get(
                'text_transform') == 'upper' else w['word'] for w in chunk['word_data']])

            # This will calculate the exact lines and their dimensions
            layout = self._layout_text_with_wrapping(
                full_text_of_chunk, base_font, max_text_width_px)
            if not layout["lines"]:
                continue

            # --- CALCULATE POSITIONS FOR EVERY WORD IN THE MULTI-LINE LAYOUT ---
            word_layout_list = []
            word_counter = 0

            # Center the entire text block vertically
            total_block_height = layout['total_height']
            y_pos_factor = dynamic_style['position'][1]
            block_start_y = (
                video_size[1] * y_pos_factor) - (total_block_height / 2)

            for line_index, line_text in enumerate(layout["lines"]):
                # Center the current line horizontally
                line_width = layout["line_widths"][line_index]
                line_start_x = (video_size[0] - line_width) / 2
                current_x = line_start_x

                # Calculate the Y position for this specific line
                line_spacing_extra = dynamic_style.get('line_spacing', 0)

                # Calculate the Y position for this specific line, including the extra spacing
                current_y = block_start_y + \
                    (line_index * (layout["line_height"] + line_spacing_extra))

                words_in_this_line = line_text.split()
                for word_text in words_in_this_line:
                    if word_counter >= len(chunk['word_data']):
                        break

                    word_info = chunk['word_data'][word_counter]

                    # Get dimensions for both states
                    bbox_inactive = base_font.getbbox(word_text)
                    w_inactive = bbox_inactive[2] - bbox_inactive[0]
                    h_inactive = bbox_inactive[3] - bbox_inactive[1]

                    bbox_active = highlight_font.getbbox(word_text)
                    w_active = bbox_active[2] - bbox_active[0]
                    h_active = bbox_active[3] - bbox_active[1]

                    word_layout_list.append({
                        'text': word_text,
                        'start_time': word_info['start'],
                        'end_time': word_info['end'],
                        'x': current_x,
                        'y': current_y,
                        'w_inactive': w_inactive,
                        'h_inactive': h_inactive,
                        'w_active': w_active,
                        'h_active': h_active
                    })

                    # Advance x-position for the next word

                    word_width = base_font.getlength(word_text)
                    # Get the spacing value from the style, defaulting to a reasonable value
                    spacing_pixels = dynamic_style.get('word_spacing', 10)
                    logger.info(
                        f"For word '{word_text}', using spacing of {spacing_pixels} pixels.")
                    current_x += word_width + spacing_pixels
                    word_counter += 1

            # --- CREATE THE VIDEO CLIP (this part is the same as before) ---
            make_frame_for_this_chunk = create_make_frame_function(
                chunk, word_layout_list)

            subtitle_clip = VideoClip(
                make_frame=lambda t, fn=make_frame_for_this_chunk: fn(t)[0],
                ismask=False, duration=chunk['duration']
            )
            alpha_mask = VideoClip(
                make_frame=lambda t, fn=make_frame_for_this_chunk: fn(t)[1],
                ismask=True, duration=chunk['duration']
            )

            final_line_clip = subtitle_clip.set_mask(
                alpha_mask).set_start(chunk['start'])
            all_clips.append(final_line_clip)

        return all_clips

    def _get_dynamic_style(self, style_config: Dict[str, Any], video_size: Tuple[int, int]) -> Dict[str, Any]:
        """
        Calculates dynamic, resolution-aware style properties based on the video's width.
        This provides a strong, consistent look for social media formats.
        """
        # Our reference point is a standard 1080px wide video.
        REFERENCE_WIDTH = 1080.0

        video_width = video_size[0]

        # The scale factor is now a simple ratio of the widths.
        scale_factor = video_width / REFERENCE_WIDTH

        # Create a copy to avoid modifying the original style dictionary
        dynamic_style = style_config.copy()

        # Scale all pixel-based values
        properties_to_scale = [
            'fontsize',
            'stroke_width',
            'line_spacing',
            # 'word_spacing',
            'shadow_blur_radius'
        ]

        for prop in properties_to_scale:
            if prop in dynamic_style:
                base_value = dynamic_style[prop]
                # Use max(1, ...) to ensure values don't become zero for very small videos
                scaled_value = max(1, int(base_value * scale_factor))
                dynamic_style[prop] = scaled_value

        # Scale the shadow offset tuple (x, y)
        if 'shadow_offset' in dynamic_style:
            base_x, base_y = dynamic_style['shadow_offset']
            scaled_x = int(base_x * scale_factor)
            scaled_y = int(base_y * scale_factor)
            dynamic_style['shadow_offset'] = (scaled_x, scaled_y)

        # Log the change for debugging
        logger.info(
            f"WIDTH SCALING: Video Width: {video_width}, "
            f"Scale Factor: {scale_factor:.2f}, "
            f"New Fontsize: {dynamic_style['fontsize']}"
        )

        return dynamic_style

    def _create_subtitle_chunks(self, transcript_data: List[Dict[str, Any]], style_config: Dict[str, Any]) -> List[Dict[str, Any]]:
        chunks, current_chunk = [], {
            'words': [], 'start': None, 'end': None, 'text': '', 'word_data': []}
        max_words = style_config.get('max_words_per_line', 4)
        valid_words = [w for w in transcript_data if w.get(
            'start') is not None and w.get('end') is not None and w['end'] > w['start']]

        if not valid_words:
            logger.warning(
                "No valid words with positive duration found in transcript for chunking.")
            return []

        for word_data in valid_words:
            if current_chunk['start'] is None:
                current_chunk['start'] = word_data['start']
            current_chunk['words'].append(word_data['word'])
            current_chunk['word_data'].append(word_data)
            current_chunk['end'] = word_data['end']

            if len(current_chunk['words']) >= max_words or word_data['word'].endswith(('.', '!', '?', ',')):
                duration = current_chunk['end'] - current_chunk['start']
                if duration > 0:
                    text = ' '.join(current_chunk['words'])
                    last_word_in_chunk = current_chunk['word_data'][-1]
                    if 'emoji' in last_word_in_chunk:
                        text += f" {last_word_in_chunk['emoji']}"
                        logger.info(
                            f"Appending emoji '{last_word_in_chunk['emoji']}' to subtitle chunk text.")

                    current_chunk['duration'] = duration
                    current_chunk['text'] = text
                    chunks.append(current_chunk)
                current_chunk = {'words': [], 'start': None,
                                 'end': None, 'text': '', 'word_data': []}

        if current_chunk['words']:
            duration = current_chunk['end'] - current_chunk['start']
            if duration > 0:
                text = ' '.join(current_chunk['words'])
                last_word_in_chunk = current_chunk['word_data'][-1]
                if 'emoji' in last_word_in_chunk:
                    text += f" {last_word_in_chunk['emoji']}"
                    logger.info(
                        f"Appending emoji '{last_word_in_chunk['emoji']}' to final subtitle chunk text.")

                current_chunk['duration'] = duration
                current_chunk['text'] = text
                chunks.append(current_chunk)

        return chunks

    def add_watermark(self, video_path: str, output_path: str, watermark_text: str):
        """
        Adds a text watermark using an OPTIMIZED OpenCV method.
        This is the final processing step.
        """
        temp_video_output = None
        try:
            logger.info(
                f"Adding watermark to {video_path} (Optimized OpenCV method)")

            cap = cv2.VideoCapture(video_path)
            if not cap.isOpened():
                raise IOError(f"Cannot open video file: {video_path}")

            frame_width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            frame_height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            fps = cap.get(cv2.CAP_PROP_FPS) or 24

            temp_dir = os.path.dirname(output_path)
            temp_video_output = os.path.join(
                temp_dir, f"temp_watermark_video_{os.path.basename(output_path)}")
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            out = cv2.VideoWriter(temp_video_output, fourcc,
                                  fps, (frame_width, frame_height))

            # --- Create the watermark layer ONCE ---
            watermark_layer = np.zeros(
                (frame_height, frame_width, 3), dtype=np.uint8)
            font_face = cv2.FONT_HERSHEY_SIMPLEX
            font_scale = 1.0
            font_color = (255, 255, 255)
            thickness = 2

            text_size, _ = cv2.getTextSize(
                watermark_text, font_face, font_scale, thickness)
            text_width, text_height = text_size
            x_step = text_width + 150
            y_step = text_height + 150

            for y in range(0, frame_height, y_step):
                for x in range(0, frame_width, x_step):
                    cv2.putText(watermark_layer, watermark_text, (x, y + text_height),
                                font_face, font_scale, font_color, thickness, cv2.LINE_AA)

            # --- Process each frame ---
            while True:
                ret, frame = cap.read()
                if not ret:
                    break

                alpha = 0.15  # Watermark transparency
                cv2.addWeighted(watermark_layer, alpha, frame, 1.0, 0, frame)
                out.write(frame)

            cap.release()
            out.release()
            logger.info(
                f"Created temporary watermarked video at {temp_video_output}")

            # --- Combine with audio from the source video using FFmpeg ---
            logger.info("Combining with original audio using FFmpeg...")
            ffmpeg_command = [
                'ffmpeg', '-y', '-i', temp_video_output, '-i', video_path,
                '-c:v', 'copy', '-c:a', 'aac', '-map', '0:v:0', '-map', '1:a:0?', '-shortest', output_path
            ]
            subprocess.run(ffmpeg_command, check=True,
                           capture_output=True, text=True)
            logger.info(
                f"Successfully watermarked video saved to {output_path}")

        except Exception as e:
            logger.error(
                f"Failed to add watermark (Optimized OpenCV): {e}\n{traceback.format_exc()}")
            logger.warning(
                f"Watermark failed. Using un-watermarked video for {output_path}")
            if os.path.exists(video_path) and video_path != output_path:
                shutil.copy(video_path, output_path)
        finally:
            if temp_video_output and os.path.exists(temp_video_output):
                os.remove(temp_video_output)

    def _cut_video(self, video_path: str, cut_duration_seconds: float, output_dir: str) -> str:
        """
        Cuts the video to a specified duration in SECONDS using a direct FFmpeg command.
        This is more robust than using MoviePy's writer.
        Returns the path to the new (potentially cut) video.
        """
        try:
            # We still use MoviePy here for one simple, fast task: getting the duration.
            with VideoFileClip(video_path) as video:
                video_duration_seconds = video.duration

            if video_duration_seconds <= cut_duration_seconds:
                logger.info(
                    f"Video duration ({video_duration_seconds:.2f}s) is shorter than or equal to target ({cut_duration_seconds}s). No cutting needed.")
                return video_path

            logger.info(
                f"Video duration ({video_duration_seconds:.2f}s) is longer than target ({cut_duration_seconds}s). Cutting video with FFmpeg...")

            cut_video_path = os.path.join(
                self.temp_dir, f"cut_{Path(video_path).name}"
            )

            # --- FFmpeg Command Definition ---
            # This command is frame-accurate because it re-encodes the clip.
            # -y: Overwrite output file if it exists
            # -i: Input file
            # -ss 0: Start cutting from the beginning
            # -t: Duration of the cut clip (in seconds)
            # -c:v libx264: Standard video codec for MP4
            # -c:a aac: Standard audio codec
            ffmpeg_command = [
                'ffmpeg',
                '-y',
                '-i', video_path,
                '-ss', '0',
                '-t', str(cut_duration_seconds),
                '-c:v', 'libx264',
                '-c:a', 'aac',
                cut_video_path
            ]

            # --- Execute the FFmpeg command ---
            # We use subprocess.run for better error handling.
            # check=True will raise a CalledProcessError if FFmpeg fails.
            result = subprocess.run(
                ffmpeg_command, check=True, capture_output=True, text=True)

            logger.info(
                f"Video cut successfully with FFmpeg. New file at: {cut_video_path}")
            return cut_video_path

        except subprocess.CalledProcessError as e:
            # This catches errors specifically from the FFmpeg command
            logger.error(
                f"FFmpeg failed during video cutting. Stderr: {e.stderr}. Using original video path.")
            return video_path
        except Exception as e:
            # This catches other errors (e.g., if MoviePy can't read the duration)
            logger.error(
                f"An unexpected error occurred during video cutting: {e}. Using original video path.", exc_info=True)
            return video_path

    def process_video_comprehensive(self, video_path: str, output_dir: str,
                                    options: Dict[str, Any] = None) -> Dict[str, Any]:
        if options is None:
            options = {}
        # --- Get all options at the start ---
        print(f'options: {options}')
        add_subtitles = options.get('add_subtitles', True)
        add_emojis = options.get('add_emojis', True)
        add_watermark = options.get('add_watermark', False)
        watermark_text = options.get('watermark_text', 'SmartClips.io')
        subtitle_style = options.get(
            'subtitle_style', 'clean_modern')  # 1. viral_style 2. karaoke_pop
        use_ai_features = options.get('use_ai_features', False)
        max_duration_seconds = options.get('max_duration_seconds', None)
        print(f"max_duration_seconds: {max_duration_seconds}")

        logger.info(
            f"Processing options: add_subtitles={add_subtitles}, add_emojis={add_emojis}, add_watermark={add_watermark}")

        results = {'original_video': video_path, 'processed_videos': {},
                   'metadata': {}, 'status': 'Processing', 'message': ''}
        start_time = datetime.now()

        # This variable will hold the path to the latest processed video file.
        current_video_path = video_path

        if max_duration_seconds is not None:
            logger.info(
                f"Clip cutting requested to a max duration of {max_duration_seconds} seconds.")
            # The _cut_video method now expects the duration in seconds.
            current_video_path = self._cut_video(
                video_path, float(max_duration_seconds), output_dir)

        try:
            logger.info(
                f"Starting comprehensive AI video processing for: {video_path}")

            # --- STAGE 1: Add Watermark (Applied FIRST) ---
            if add_watermark:
                logger.info(
                    f"Applying watermark FIRST to: {current_video_path}")
                watermarked_path = os.path.join(
                    output_dir, f"video_watermarked_{Path(video_path).stem}.mp4")

                self.add_watermark(
                    video_path=current_video_path,
                    output_path=watermarked_path,
                    watermark_text=watermark_text
                )

                current_video_path = watermarked_path
                logger.info(
                    f"Watermark added. Current video for next step is: {current_video_path}")
            else:
                logger.info("Skipping watermark creation as per options.")

            # --- STAGE 2: Transcription and Content Analysis (including Emojis) ---
            transcript_data = self.extract_transcript_with_timing(
                current_video_path)
            if not transcript_data:
                results.update({'status': 'Completed with Warning',
                               'message': 'No audio detected or video is silent.'})
                return results

            if use_ai_features and self.openai_api_key:
                transcript_data = self.analyze_content_with_ai(transcript_data)

            # <-- EMOJI ANALYSIS: The transcript is updated with emoji data here.
            if add_emojis:
                logger.info("Running robust fallback emoji system...")
                transcript_data = self._add_fallback_emojis(transcript_data)

            results['metadata']['transcript'] = transcript_data

            # --- FINAL STAGE: Add Subtitles & Emojis (Applied to the watermarked video) ---
            if add_subtitles:
                logger.info(
                    f"Creating AI-enhanced subtitles and emojis on: {current_video_path}")
                final_output_path = os.path.join(
                    output_dir, f"final_video_with_subtitles_{Path(video_path).stem}.mp4")

                self.create_ai_enhanced_subtitles(
                    current_video_path,
                    transcript_data,
                    final_output_path,
                    subtitle_style,
                    enable_emoji_overlays=add_emojis,
                    enable_importance_highlighting=use_ai_features
                )
                results['processed_videos']['final_output'] = final_output_path

            results.update(
                {'status': 'Success', 'message': 'Video processed successfully.'})

        except Exception as e:
            logger.error(
                f"FATAL error in comprehensive video processing: {e}", exc_info=True)
            results.update({'status': 'Error', 'message': str(e)})
        finally:
            results['processing_time'] = (
                datetime.now() - start_time).total_seconds()
            logger.info(
                f"Processing finished in {results['processing_time']:.2f}s. Status: {results['status']}")
            return results


def process_video_with_enhancements(video_path: str, output_dir: str,
                                    openai_api_key: str = None,
                                    options: Dict[str, Any] = None) -> Dict[str, Any]:
    """
    Convenience function to process a video with all AI enhancements
    """
    processor = AdvancedVideoProcessor(openai_api_key=openai_api_key)
    return processor.process_video_comprehensive(video_path, output_dir, options)


def create_quick_ai_tiktok_clip(video_path: str, output_path: str,
                                openai_api_key: str = None) -> str:
    """
    Quick function to create a TikTok-ready clip with AI-powered features
    """
    processor = AdvancedVideoProcessor(openai_api_key=openai_api_key)

    transcript_data = processor.extract_transcript_with_timing(video_path)

    if openai_api_key:
        transcript_data = processor.analyze_content_with_ai(transcript_data)
    else:
        transcript_data = processor.analyze_emotions_and_add_emojis(
            transcript_data)

    processor.create_ai_enhanced_subtitles(
        video_path,
        transcript_data,
        output_path,
        'clean_modern',
        enable_emoji_overlays=True,
        enable_importance_highlighting=True
    )

    return output_path


def create_video_with_emoji_overlays_only(video_path: str, output_path: str,
                                          openai_api_key: str = None,
                                          style: str = "clean_modern") -> str:
    """
    Create a video with only emoji overlays (no importance highlighting)
    """
    processor = AdvancedVideoProcessor(openai_api_key=openai_api_key)

    # Extract transcript
    transcript_data = processor.extract_transcript_with_timing(video_path)

    # Add emojis
    if openai_api_key:
        transcript_data = processor.analyze_content_with_ai(transcript_data)
    else:
        transcript_data = processor.analyze_emotions_and_add_emojis(
            transcript_data)

    # Create video with only emoji overlays
    processor.create_ai_enhanced_subtitles(
        video_path,
        transcript_data,
        output_path,
        style,
        enable_emoji_overlays=True,
        enable_importance_highlighting=False

    )

    return output_path


def create_video_with_importance_highlighting_only(video_path: str, output_path: str,
                                                   openai_api_key: str = None,
                                                   style: str = "clean_modern") -> str:
    """
    Create a video with only importance highlighting (no emoji overlays)
    """
    processor = AdvancedVideoProcessor(openai_api_key=openai_api_key)

    # Extract transcript
    transcript_data = processor.extract_transcript_with_timing(video_path)

    # AI analysis for importance detection
    if openai_api_key:
        transcript_data = processor.analyze_content_with_ai(transcript_data)

    # Create video with only importance highlighting
    processor.create_ai_enhanced_subtitles(
        video_path,
        transcript_data,
        output_path,
        style,
        enable_emoji_overlays=False,
        enable_importance_highlighting=True
    )

    return output_path


def analyze_video_importance(video_path: str, openai_api_key: str = None) -> Dict[str, Any]:
    """
    Analyze a video to detect important segments that would be highlighted
    """
    processor = AdvancedVideoProcessor(openai_api_key=openai_api_key)

    # Extract transcript
    transcript_data = processor.extract_transcript_with_timing(video_path)

    # AI analysis
    if openai_api_key:
        transcript_data = processor.analyze_content_with_ai(transcript_data)

    # Detect important segments
    important_segments = processor.detect_important_text_segments(
        transcript_data)

    return {
        'total_words': len(transcript_data),
        'important_segments': important_segments,
        'importance_count': len(important_segments),
        'ai_features_used': bool(openai_api_key),
        'video_duration': transcript_data[-1]['end'] if transcript_data else 0
    }
