"""
Comprehensive test to verify facial AI zoom functionality
Tests actual video processing and zoom effects with detailed logging
"""

import os
import time
import tempfile
import cv2
import numpy as np
import logging
from facial_ai_processor import FacialAIProcessor, ZoomConfig

# Set up detailed logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class ZoomFunctionalityTester:
    """Test class to verify zoom functionality with detailed feedback"""
    
    def __init__(self):
        self.processor = FacialAIProcessor()
        logger.info("🚀 Initialized FacialAIProcessor for zoom testing")
    
    def create_realistic_test_video(self, duration_seconds=10):
        """Create a realistic test video with clear face-like objects"""
        temp_file = tempfile.mktemp(suffix=".mp4")
        logger.info(f"📹 Creating test video: {temp_file}")
        
        # Video parameters
        fps = 30
        width, height = 640, 480
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        out = cv2.VideoWriter(temp_file, fourcc, fps, (width, height))
        
        total_frames = duration_seconds * fps
        logger.info(f"📊 Video specs: {width}x{height} @ {fps}fps, {total_frames} frames")
        
        for frame_num in range(total_frames):
            # Create a realistic background
            frame = np.random.randint(20, 60, (height, width, 3), dtype=np.uint8)
            
            # Add a realistic moving "face" - oval shape with features
            center_x = 200 + int(100 * np.sin(frame_num * 0.05))  # Slower movement
            center_y = 200 + int(50 * np.cos(frame_num * 0.03))
            
            # Draw face oval
            cv2.ellipse(frame, (center_x, center_y), (60, 80), 0, 0, 360, (220, 180, 160), -1)
            
            # Add facial features
            # Eyes
            cv2.circle(frame, (center_x - 20, center_y - 20), 8, (50, 50, 50), -1)
            cv2.circle(frame, (center_x + 20, center_y - 20), 8, (50, 50, 50), -1)
            
            # Nose
            cv2.circle(frame, (center_x, center_y), 5, (180, 140, 120), -1)
            
            # Mouth
            cv2.ellipse(frame, (center_x, center_y + 25), (15, 8), 0, 0, 180, (100, 50, 50), -1)
            
            # Add some noise to make it more realistic
            noise = np.random.randint(-10, 10, frame.shape, dtype=np.int16)
            frame = np.clip(frame.astype(np.int16) + noise, 0, 255).astype(np.uint8)
            
            out.write(frame)
            
            if frame_num % (fps * 2) == 0:  # Log every 2 seconds
                logger.debug(f"📝 Generated frame {frame_num}/{total_frames} - Face at ({center_x}, {center_y})")
        
        out.release()
        logger.info(f"✅ Test video created successfully: {os.path.getsize(temp_file)} bytes")
        return temp_file
    
    def test_analysis_phase(self, video_path, config):
        """Test the analysis phase and log detailed results"""
        logger.info("🔍 Starting video analysis phase...")
        
        try:
            analysis_result = self.processor.analyze_video(video_path, config)
            
            logger.info("📊 ANALYSIS RESULTS:")
            logger.info(f"  - Video duration: {analysis_result.get('duration', 'N/A')}s")
            logger.info(f"  - Face detections: {analysis_result.get('face_detections', 0)}")
            logger.info(f"  - Voice segments: {analysis_result.get('voice_segments', 0)}")
            
            speaker_segments = analysis_result.get('speaker_segments', [])
            logger.info(f"  - Speaker segments: {len(speaker_segments)}")
            
            for i, segment in enumerate(speaker_segments[:3]):  # Log first 3 segments
                logger.info(f"    Segment {i+1}: {segment.get('start_time', 0):.2f}s - {segment.get('end_time', 0):.2f}s")
                logger.info(f"      Speaker ID: {segment.get('speaker_id', 'N/A')}")
                logger.info(f"      Face bbox: {segment.get('face_bbox', 'N/A')}")
                logger.info(f"      Confidence: {segment.get('confidence', 0):.2f}")
            
            zoom_timeline = analysis_result.get('zoom_timeline', [])
            logger.info(f"  - Zoom timeline entries: {len(zoom_timeline)}")
            
            for i, zoom_entry in enumerate(zoom_timeline[:3]):  # Log first 3 zoom entries
                logger.info(f"    Zoom {i+1}: {zoom_entry.get('start_time', 0):.2f}s - {zoom_entry.get('end_time', 0):.2f}s")
                logger.info(f"      Zoom level: {zoom_entry.get('zoom_level', 1.0)}x")
                logger.info(f"      Target bbox: {zoom_entry.get('target_bbox', 'N/A')}")
                logger.info(f"      Transition speed: {zoom_entry.get('transition_speed', 0.5)}s")
            
            return analysis_result
            
        except Exception as e:
            logger.error(f"❌ Analysis failed: {e}")
            raise
    
    def test_processing_phase(self, video_path, config):
        """Test the actual video processing with zoom effects"""
        logger.info("🎬 Starting video processing phase...")

        try:
            # Create output path
            output_path = tempfile.mktemp(suffix="_processed.mp4")

            # Process the video with zoom effects
            start_time = time.time()
            processed_output_path = self.processor.process_video_with_face_zoom(
                video_path,
                output_path,
                config
            )
            processing_time = time.time() - start_time

            logger.info("🎯 PROCESSING RESULTS:")
            logger.info(f"  - Input video: {video_path}")
            logger.info(f"  - Output video: {processed_output_path}")
            logger.info(f"  - Processing time: {processing_time:.2f}s")

            # Verify output file exists and has content
            output_path = processed_output_path
            if output_path and os.path.exists(output_path):
                output_size = os.path.getsize(output_path)
                logger.info(f"  - Output file size: {output_size} bytes")
                
                if output_size > 0:
                    logger.info("✅ Output video file created successfully")
                    return self.verify_zoom_effects(output_path, video_path, config)
                else:
                    logger.error("❌ Output video file is empty")
                    return False
            else:
                logger.error("❌ Output video file not created")
                return False
                
        except Exception as e:
            logger.error(f"❌ Processing failed: {e}")
            raise
    
    def verify_zoom_effects(self, processed_video, original_video, config):
        """Verify that zoom effects were actually applied by comparing frames"""
        logger.info("🔍 Verifying zoom effects by comparing original vs processed video...")
        
        try:
            # Open both videos
            original_cap = cv2.VideoCapture(original_video)
            processed_cap = cv2.VideoCapture(processed_video)
            
            if not original_cap.isOpened() or not processed_cap.isOpened():
                logger.error("❌ Could not open video files for comparison")
                return False
            
            # Get video properties
            orig_fps = original_cap.get(cv2.CAP_PROP_FPS)
            proc_fps = processed_cap.get(cv2.CAP_PROP_FPS)
            orig_frames = int(original_cap.get(cv2.CAP_PROP_FRAME_COUNT))
            proc_frames = int(processed_cap.get(cv2.CAP_PROP_FRAME_COUNT))
            
            logger.info(f"📊 Video comparison:")
            logger.info(f"  - Original: {orig_frames} frames @ {orig_fps}fps")
            logger.info(f"  - Processed: {proc_frames} frames @ {proc_fps}fps")
            
            # Sample frames at different time points
            sample_points = [0.2, 0.4, 0.6, 0.8]  # 20%, 40%, 60%, 80% through video
            zoom_detected = False
            
            for sample_point in sample_points:
                # Set frame positions
                orig_frame_num = int(orig_frames * sample_point)
                proc_frame_num = int(proc_frames * sample_point)
                
                original_cap.set(cv2.CAP_PROP_POS_FRAMES, orig_frame_num)
                processed_cap.set(cv2.CAP_PROP_POS_FRAMES, proc_frame_num)
                
                ret1, orig_frame = original_cap.read()
                ret2, proc_frame = processed_cap.read()
                
                if ret1 and ret2:
                    # Compare frame characteristics
                    zoom_effect = self.detect_zoom_effect(orig_frame, proc_frame, sample_point)
                    if zoom_effect:
                        zoom_detected = True
                        logger.info(f"✅ Zoom effect detected at {sample_point*100:.0f}% through video")
                    else:
                        logger.debug(f"📝 No zoom effect at {sample_point*100:.0f}% through video")
            
            original_cap.release()
            processed_cap.release()
            
            if zoom_detected:
                logger.info("🎉 SUCCESS: Zoom effects are working correctly!")
                return True
            else:
                logger.warning("⚠️  WARNING: No zoom effects detected in processed video")
                return False
                
        except Exception as e:
            logger.error(f"❌ Zoom verification failed: {e}")
            return False
    
    def detect_zoom_effect(self, original_frame, processed_frame, time_point):
        """Detect if zoom effect was applied by analyzing frame differences"""
        try:
            # Convert to grayscale for analysis
            orig_gray = cv2.cvtColor(original_frame, cv2.COLOR_BGR2GRAY)
            proc_gray = cv2.cvtColor(processed_frame, cv2.COLOR_BGR2GRAY)
            
            # Calculate center regions (where zoom would be most apparent)
            h, w = orig_gray.shape
            center_region = (
                slice(h//4, 3*h//4),
                slice(w//4, 3*w//4)
            )
            
            orig_center = orig_gray[center_region]
            proc_center = proc_gray[center_region]
            
            # Calculate mean pixel intensity in center region
            orig_mean = np.mean(orig_center)
            proc_mean = np.mean(proc_center)
            
            # Calculate standard deviation (measure of detail/sharpness)
            orig_std = np.std(orig_center)
            proc_std = np.std(proc_center)
            
            # If zoom was applied, we expect:
            # 1. Different mean values (due to different content being centered)
            # 2. Potentially different standard deviation (due to magnification)
            mean_diff = abs(orig_mean - proc_mean)
            std_diff = abs(orig_std - proc_std)
            
            logger.debug(f"📊 Frame analysis at {time_point*100:.0f}%:")
            logger.debug(f"  - Mean intensity diff: {mean_diff:.2f}")
            logger.debug(f"  - Std deviation diff: {std_diff:.2f}")
            
            # Threshold for detecting zoom (these are heuristic values)
            if mean_diff > 5 or std_diff > 3:
                logger.debug(f"  - 🎯 Zoom effect likely detected (mean_diff={mean_diff:.2f}, std_diff={std_diff:.2f})")
                return True
            else:
                logger.debug(f"  - 📝 No significant zoom effect detected")
                return False
                
        except Exception as e:
            logger.error(f"❌ Frame analysis failed: {e}")
            return False
    
    def run_comprehensive_test(self):
        """Run comprehensive zoom functionality test"""
        logger.info("🚀 Starting comprehensive zoom functionality test")
        logger.info("=" * 60)
        
        # Test configurations
        test_configs = [
            {
                "name": "Moderate Zoom",
                "config": ZoomConfig(zoom_level=1.5, detection_sensitivity=0.7, transition_speed=0.5)
            },
            {
                "name": "High Zoom",
                "config": ZoomConfig(zoom_level=2.0, detection_sensitivity=0.8, transition_speed=0.3)
            }
        ]
        
        results = []
        
        for test_case in test_configs:
            logger.info(f"\n🧪 Testing: {test_case['name']}")
            logger.info("-" * 40)
            
            # Create test video
            video_path = None
            try:
                video_path = self.create_realistic_test_video(duration_seconds=8)
                
                # Test analysis
                analysis_result = self.test_analysis_phase(video_path, test_case['config'])
                
                # Test processing
                processing_success = self.test_processing_phase(video_path, test_case['config'])
                
                result = {
                    "test_name": test_case['name'],
                    "analysis_success": analysis_result is not None,
                    "processing_success": processing_success,
                    "face_detections": analysis_result.get('face_detections', 0) if analysis_result else 0,
                    "zoom_timeline_entries": len(analysis_result.get('zoom_timeline', [])) if analysis_result else 0
                }
                
                results.append(result)
                
                if processing_success:
                    logger.info(f"✅ {test_case['name']} test PASSED")
                else:
                    logger.warning(f"⚠️  {test_case['name']} test FAILED")
                
            except Exception as e:
                logger.error(f"❌ {test_case['name']} test CRASHED: {e}")
                results.append({
                    "test_name": test_case['name'],
                    "analysis_success": False,
                    "processing_success": False,
                    "error": str(e)
                })
            
            finally:
                # Clean up
                if video_path and os.path.exists(video_path):
                    os.remove(video_path)
                    logger.debug(f"🧹 Cleaned up test video: {video_path}")
        
        # Generate final report
        self.generate_test_report(results)
        
        return results
    
    def generate_test_report(self, results):
        """Generate comprehensive test report"""
        logger.info("\n" + "=" * 60)
        logger.info("🎯 ZOOM FUNCTIONALITY TEST REPORT")
        logger.info("=" * 60)
        
        total_tests = len(results)
        passed_tests = sum(1 for r in results if r.get('processing_success', False))
        
        logger.info(f"📊 Test Summary: {passed_tests}/{total_tests} tests passed")
        
        for result in results:
            logger.info(f"\n🧪 {result['test_name']}:")
            logger.info(f"  - Analysis: {'✅ PASS' if result.get('analysis_success') else '❌ FAIL'}")
            logger.info(f"  - Processing: {'✅ PASS' if result.get('processing_success') else '❌ FAIL'}")
            
            if 'error' in result:
                logger.info(f"  - Error: {result['error']}")
            else:
                logger.info(f"  - Face detections: {result.get('face_detections', 0)}")
                logger.info(f"  - Zoom timeline entries: {result.get('zoom_timeline_entries', 0)}")
        
        logger.info("\n🎯 CONCLUSIONS:")
        if passed_tests == total_tests:
            logger.info("🎉 ALL TESTS PASSED - Zoom functionality is working correctly!")
            logger.info("✅ Face detection is working")
            logger.info("✅ Zoom timeline generation is working")
            logger.info("✅ Video processing with zoom effects is working")
            logger.info("✅ Output video verification is working")
        elif passed_tests > 0:
            logger.info(f"⚠️  PARTIAL SUCCESS - {passed_tests}/{total_tests} tests passed")
            logger.info("🔧 Some zoom configurations may need adjustment")
        else:
            logger.info("❌ ALL TESTS FAILED - Zoom functionality needs debugging")
            logger.info("🔧 Check MediaPipe installation and video processing pipeline")
        
        logger.info("=" * 60)

def main():
    """Main test execution"""
    logger.info("🎬 Facial AI Zoom Functionality Test")
    logger.info("This test will verify that zoom effects are actually applied to videos")
    
    tester = ZoomFunctionalityTester()
    results = tester.run_comprehensive_test()
    
    # Return success status
    success = all(r.get('processing_success', False) for r in results)
    return success

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
