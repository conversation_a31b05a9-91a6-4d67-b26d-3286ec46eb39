"""
Performance benchmarking tests for Facial AI system
Tests processing speed, memory usage, and scalability
"""

import time
import psutil
import os
import tempfile
import cv2
import numpy as np
from facial_ai_processor import FacialAIProcessor, ZoomConfig

class PerformanceBenchmark:
    """Performance benchmarking suite for Facial AI"""
    
    def __init__(self):
        self.processor = FacialAIProcessor()
        self.results = {}
    
    def create_test_video(self, duration_seconds=10, fps=30, resolution=(640, 480)):
        """Create a test video with specified parameters"""
        temp_file = tempfile.mktemp(suffix=".mp4")
        
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        out = cv2.VideoWriter(temp_file, fourcc, fps, resolution)
        
        total_frames = duration_seconds * fps
        
        for frame_num in range(total_frames):
            # Create frame with moving elements
            frame = np.zeros((resolution[1], resolution[0], 3), dtype=np.uint8)
            
            # Add multiple moving rectangles (simulating faces)
            for i in range(3):  # 3 simulated speakers
                x = 100 + i * 200 + int(30 * np.sin(frame_num * 0.1 + i))
                y = 150 + int(20 * np.cos(frame_num * 0.1 + i))
                cv2.rectangle(frame, (x, y), (x + 80, y + 100), (255, 255, 255), -1)
                
                # Add some facial features
                cv2.circle(frame, (x + 20, y + 30), 5, (0, 0, 0), -1)  # Eye
                cv2.circle(frame, (x + 60, y + 30), 5, (0, 0, 0), -1)  # Eye
                cv2.rectangle(frame, (x + 30, y + 60), (x + 50, y + 70), (0, 0, 0), -1)  # Mouth
            
            out.write(frame)
        
        out.release()
        return temp_file
    
    def measure_memory_usage(self):
        """Get current memory usage"""
        process = psutil.Process(os.getpid())
        return process.memory_info().rss / 1024 / 1024  # MB
    
    def benchmark_face_detection_speed(self):
        """Benchmark face detection processing speed"""
        print("Benchmarking face detection speed...")
        
        # Test different video lengths
        test_cases = [
            {"duration": 5, "fps": 30, "resolution": (640, 480)},
            {"duration": 10, "fps": 30, "resolution": (640, 480)},
            {"duration": 5, "fps": 30, "resolution": (1280, 720)},
            {"duration": 5, "fps": 60, "resolution": (640, 480)},
        ]
        
        results = []
        
        for case in test_cases:
            print(f"Testing {case['duration']}s video at {case['resolution']} @ {case['fps']}fps...")
            
            # Create test video
            video_path = self.create_test_video(
                duration_seconds=case['duration'],
                fps=case['fps'],
                resolution=case['resolution']
            )
            
            try:
                # Measure memory before
                memory_before = self.measure_memory_usage()
                
                # Time the analysis
                start_time = time.time()
                analysis_result = self.processor.analyze_video(video_path, ZoomConfig())
                end_time = time.time()
                
                # Measure memory after
                memory_after = self.measure_memory_usage()
                
                processing_time = end_time - start_time
                fps_processed = (case['duration'] * case['fps']) / processing_time
                memory_used = memory_after - memory_before
                
                result = {
                    "video_duration": case['duration'],
                    "video_fps": case['fps'],
                    "resolution": case['resolution'],
                    "processing_time": processing_time,
                    "fps_processed": fps_processed,
                    "memory_used_mb": memory_used,
                    "face_detections": analysis_result.get('face_detections', 0),
                    "voice_segments": analysis_result.get('voice_segments', 0)
                }
                
                results.append(result)
                
                print(f"  Processing time: {processing_time:.2f}s")
                print(f"  FPS processed: {fps_processed:.2f}")
                print(f"  Memory used: {memory_used:.2f}MB")
                print(f"  Face detections: {result['face_detections']}")
                
            except Exception as e:
                print(f"  Error: {e}")
                result = {
                    "video_duration": case['duration'],
                    "video_fps": case['fps'],
                    "resolution": case['resolution'],
                    "error": str(e)
                }
                results.append(result)
            
            finally:
                # Clean up
                if os.path.exists(video_path):
                    os.remove(video_path)
        
        self.results['face_detection_speed'] = results
        return results
    
    def benchmark_configuration_impact(self):
        """Benchmark impact of different configuration settings"""
        print("Benchmarking configuration impact...")
        
        # Create a standard test video
        video_path = self.create_test_video(duration_seconds=5, fps=30)
        
        try:
            configs = [
                {"name": "Low Sensitivity", "sensitivity": 0.3, "zoom": 1.2},
                {"name": "Medium Sensitivity", "sensitivity": 0.7, "zoom": 1.5},
                {"name": "High Sensitivity", "sensitivity": 0.9, "zoom": 2.0},
            ]
            
            results = []
            
            for config in configs:
                print(f"Testing {config['name']}...")
                
                zoom_config = ZoomConfig(
                    detection_sensitivity=config['sensitivity'],
                    zoom_level=config['zoom']
                )
                
                memory_before = self.measure_memory_usage()
                start_time = time.time()
                
                analysis_result = self.processor.analyze_video(video_path, zoom_config)
                
                end_time = time.time()
                memory_after = self.measure_memory_usage()
                
                result = {
                    "config_name": config['name'],
                    "sensitivity": config['sensitivity'],
                    "zoom_level": config['zoom'],
                    "processing_time": end_time - start_time,
                    "memory_used_mb": memory_after - memory_before,
                    "face_detections": analysis_result.get('face_detections', 0),
                    "voice_segments": analysis_result.get('voice_segments', 0)
                }
                
                results.append(result)
                
                print(f"  Processing time: {result['processing_time']:.2f}s")
                print(f"  Face detections: {result['face_detections']}")
                print(f"  Memory used: {result['memory_used_mb']:.2f}MB")
        
        finally:
            if os.path.exists(video_path):
                os.remove(video_path)
        
        self.results['configuration_impact'] = results
        return results
    
    def benchmark_memory_scaling(self):
        """Benchmark memory usage with increasing video length"""
        print("Benchmarking memory scaling...")
        
        durations = [1, 2, 5, 10, 15]  # seconds
        results = []
        
        for duration in durations:
            print(f"Testing {duration}s video...")
            
            video_path = self.create_test_video(duration_seconds=duration)
            
            try:
                memory_before = self.measure_memory_usage()
                
                # Process video
                analysis_result = self.processor.analyze_video(video_path, ZoomConfig())
                
                memory_after = self.measure_memory_usage()
                memory_used = memory_after - memory_before
                
                result = {
                    "duration": duration,
                    "memory_used_mb": memory_used,
                    "memory_per_second": memory_used / duration,
                    "face_detections": analysis_result.get('face_detections', 0),
                    "detections_per_second": analysis_result.get('face_detections', 0) / duration
                }
                
                results.append(result)
                
                print(f"  Memory used: {memory_used:.2f}MB")
                print(f"  Memory per second: {result['memory_per_second']:.2f}MB/s")
                
            except Exception as e:
                print(f"  Error: {e}")
                result = {"duration": duration, "error": str(e)}
                results.append(result)
            
            finally:
                if os.path.exists(video_path):
                    os.remove(video_path)
        
        self.results['memory_scaling'] = results
        return results
    
    def benchmark_concurrent_processing(self):
        """Benchmark concurrent processing capabilities"""
        print("Benchmarking concurrent processing...")
        
        # Create multiple test videos
        video_paths = []
        try:
            for i in range(3):
                video_path = self.create_test_video(duration_seconds=3)
                video_paths.append(video_path)
            
            # Sequential processing
            memory_before = self.measure_memory_usage()
            start_time = time.time()
            
            sequential_results = []
            for video_path in video_paths:
                result = self.processor.analyze_video(video_path, ZoomConfig())
                sequential_results.append(result)
            
            sequential_time = time.time() - start_time
            memory_after = self.measure_memory_usage()
            
            result = {
                "num_videos": len(video_paths),
                "sequential_time": sequential_time,
                "average_time_per_video": sequential_time / len(video_paths),
                "memory_used_mb": memory_after - memory_before,
                "total_face_detections": sum(r.get('face_detections', 0) for r in sequential_results)
            }
            
            print(f"  Sequential processing time: {sequential_time:.2f}s")
            print(f"  Average time per video: {result['average_time_per_video']:.2f}s")
            print(f"  Memory used: {result['memory_used_mb']:.2f}MB")
            
        finally:
            # Clean up
            for video_path in video_paths:
                if os.path.exists(video_path):
                    os.remove(video_path)
        
        self.results['concurrent_processing'] = result
        return result
    
    def generate_performance_report(self):
        """Generate a comprehensive performance report"""
        print("\n" + "=" * 60)
        print("FACIAL AI PERFORMANCE BENCHMARK REPORT")
        print("=" * 60)
        
        # System information
        print(f"System: {psutil.cpu_count()} CPU cores, {psutil.virtual_memory().total / 1024**3:.1f}GB RAM")
        print(f"Python process memory: {self.measure_memory_usage():.2f}MB")
        print()
        
        # Face detection speed results
        if 'face_detection_speed' in self.results:
            print("FACE DETECTION SPEED RESULTS:")
            print("-" * 40)
            for result in self.results['face_detection_speed']:
                if 'error' not in result:
                    print(f"Video: {result['video_duration']}s @ {result['resolution']} ({result['video_fps']}fps)")
                    print(f"  Processing: {result['processing_time']:.2f}s ({result['fps_processed']:.1f} FPS)")
                    print(f"  Memory: {result['memory_used_mb']:.2f}MB")
                    print(f"  Detections: {result['face_detections']}")
                else:
                    print(f"Video: {result['video_duration']}s @ {result['resolution']} - ERROR: {result['error']}")
            print()
        
        # Configuration impact results
        if 'configuration_impact' in self.results:
            print("CONFIGURATION IMPACT RESULTS:")
            print("-" * 40)
            for result in self.results['configuration_impact']:
                print(f"{result['config_name']}:")
                print(f"  Processing time: {result['processing_time']:.2f}s")
                print(f"  Face detections: {result['face_detections']}")
                print(f"  Memory used: {result['memory_used_mb']:.2f}MB")
            print()
        
        # Memory scaling results
        if 'memory_scaling' in self.results:
            print("MEMORY SCALING RESULTS:")
            print("-" * 40)
            for result in self.results['memory_scaling']:
                if 'error' not in result:
                    print(f"{result['duration']}s video: {result['memory_used_mb']:.2f}MB ({result['memory_per_second']:.2f}MB/s)")
                else:
                    print(f"{result['duration']}s video: ERROR - {result['error']}")
            print()
        
        # Concurrent processing results
        if 'concurrent_processing' in self.results:
            result = self.results['concurrent_processing']
            print("CONCURRENT PROCESSING RESULTS:")
            print("-" * 40)
            print(f"Videos processed: {result['num_videos']}")
            print(f"Total time: {result['sequential_time']:.2f}s")
            print(f"Average per video: {result['average_time_per_video']:.2f}s")
            print(f"Memory used: {result['memory_used_mb']:.2f}MB")
            print()
        
        # Performance recommendations
        print("PERFORMANCE RECOMMENDATIONS:")
        print("-" * 40)
        print("• For best speed: Use lower detection sensitivity (0.3-0.5)")
        print("• For best accuracy: Use higher detection sensitivity (0.7-0.9)")
        print("• For memory efficiency: Process videos in smaller chunks")
        print("• For production: Consider GPU acceleration for large videos")
        print("• Optimal resolution: 640x480 for real-time processing")
        print("• Recommended FPS: 30fps for balanced performance")

def run_performance_benchmarks():
    """Run all performance benchmarks"""
    benchmark = PerformanceBenchmark()
    
    try:
        # Run all benchmarks
        benchmark.benchmark_face_detection_speed()
        benchmark.benchmark_configuration_impact()
        benchmark.benchmark_memory_scaling()
        benchmark.benchmark_concurrent_processing()
        
        # Generate report
        benchmark.generate_performance_report()
        
        return True
        
    except Exception as e:
        print(f"Benchmark failed: {e}")
        return False

if __name__ == "__main__":
    print("Starting Facial AI Performance Benchmarks...")
    print("This may take several minutes to complete.\n")
    
    success = run_performance_benchmarks()
    
    if success:
        print("\n🎉 Performance benchmarking completed successfully!")
    else:
        print("\n❌ Performance benchmarking failed!")
