# font_checker.py
import matplotlib.font_manager

print("--- Searching for system fonts... ---")

found_komika = False
# List all .ttf font files found on your system
for font in matplotlib.font_manager.findSystemFonts(fontpaths=None, fontext='ttf'):
    # Check if the path contains 'komika'
    if 'komika' in font.lower():
        print(f"\n>>> FOUND IT! <<<\n")
        print(f"This is the exact path you need to copy:\n{font}\n")
        found_komika = True

if not found_komika:
    print("\n--- 'Komika' font not found automatically. Printing all fonts instead. ---")
    print("Please look through the list below for your font file (e.g., something with 'KOMIKAX_')")
    for font in matplotlib.font_manager.findSystemFonts(fontpaths=None, fontext='ttf'):
        print(font)

print("\n--- Search complete. ---")
