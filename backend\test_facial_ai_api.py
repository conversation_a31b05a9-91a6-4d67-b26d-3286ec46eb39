"""
API endpoint tests for Facial AI functionality
"""

import pytest
import tempfile
import os
import json
from fastapi.testclient import TestClient
from unittest.mock import patch, Mock, AsyncMock
import cv2
import numpy as np

# Import the FastAPI app
from main import app

class TestFacialAIAPI:
    """Test suite for Facial AI API endpoints"""
    
    @pytest.fixture
    def client(self):
        """Create a test client"""
        return TestClient(app)
    
    @pytest.fixture
    def auth_headers(self):
        """Mock authentication headers"""
        # In a real test, you would create a valid JWT token
        return {"Authorization": "Bearer test_token"}
    
    @pytest.fixture
    def sample_video_file(self):
        """Create a sample video file for testing"""
        temp_file = tempfile.mktemp(suffix=".mp4")
        
        # Create a simple test video
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        out = cv2.VideoWriter(temp_file, fourcc, 30.0, (640, 480))
        
        for frame_num in range(90):  # 3 seconds at 30 fps
            frame = np.zeros((480, 640, 3), dtype=np.uint8)
            # Add a moving rectangle
            x = 200 + int(50 * np.sin(frame_num * 0.1))
            y = 200
            cv2.rectangle(frame, (x, y), (x + 100, y + 120), (255, 255, 255), -1)
            out.write(frame)
        
        out.release()
        yield temp_file
        
        if os.path.exists(temp_file):
            os.remove(temp_file)
    
    @pytest.fixture
    def sample_config(self):
        """Sample face detection configuration"""
        return {
            "zoom_level": 1.5,
            "transition_speed": 0.5,
            "detection_sensitivity": 0.7,
            "voice_threshold": 0.3,
            "padding": 50
        }
    
    def test_analyze_endpoint_with_file_upload(self, client, sample_video_file, sample_config, auth_headers):
        """Test the analyze endpoint with file upload"""
        with patch('main.get_current_user') as mock_auth:
            # Mock authenticated user
            mock_user = Mock()
            mock_user.id = 1
            mock_auth.return_value = mock_user
            
            with patch('main.facial_ai_processor.analyze_video') as mock_analyze:
                # Mock analysis result
                mock_analyze.return_value = {
                    "video_path": sample_video_file,
                    "duration": 3.0,
                    "face_detections": 15,
                    "voice_segments": 2,
                    "speaker_segments": [
                        {
                            "start_time": 0.5,
                            "end_time": 2.0,
                            "speaker_id": 0,
                            "face_bbox": (100, 100, 50, 60),
                            "confidence": 0.85
                        }
                    ],
                    "zoom_timeline": [],
                    "config": sample_config,
                    "timestamp": "2024-01-01T00:00:00"
                }
                
                with open(sample_video_file, 'rb') as video_file:
                    response = client.post(
                        "/api/face-detection/analyze",
                        files={"file": ("test_video.mp4", video_file, "video/mp4")},
                        data={"request": json.dumps({"config": sample_config})},
                        headers=auth_headers
                    )
                
                assert response.status_code == 200
                result = response.json()
                assert result["success"] is True
                assert "analysis_data" in result
                assert result["analysis_data"]["face_detections"] == 15
                assert result["analysis_data"]["voice_segments"] == 2
    
    def test_analyze_endpoint_with_url(self, client, sample_config, auth_headers):
        """Test the analyze endpoint with video URL"""
        with patch('main.get_current_user') as mock_auth:
            mock_user = Mock()
            mock_user.id = 1
            mock_auth.return_value = mock_user
            
            with patch('main.url_processor.download_video') as mock_download, \
                 patch('main.facial_ai_processor.analyze_video') as mock_analyze:
                
                # Mock video download
                mock_download.return_value = "/tmp/downloaded_video.mp4"
                
                # Mock analysis result
                mock_analyze.return_value = {
                    "video_path": "/tmp/downloaded_video.mp4",
                    "duration": 5.0,
                    "face_detections": 25,
                    "voice_segments": 3,
                    "speaker_segments": [],
                    "zoom_timeline": [],
                    "config": sample_config,
                    "timestamp": "2024-01-01T00:00:00"
                }
                
                response = client.post(
                    "/api/face-detection/analyze",
                    json={
                        "video_url": "https://example.com/test_video.mp4",
                        "config": sample_config
                    },
                    headers=auth_headers
                )
                
                assert response.status_code == 200
                result = response.json()
                assert result["success"] is True
                assert result["analysis_data"]["face_detections"] == 25
    
    def test_process_endpoint_starts_background_task(self, client, sample_video_file, sample_config, auth_headers):
        """Test that the process endpoint starts a background task"""
        with patch('main.get_current_user') as mock_auth:
            mock_user = Mock()
            mock_user.id = 1
            mock_auth.return_value = mock_user
            
            with open(sample_video_file, 'rb') as video_file:
                response = client.post(
                    "/api/face-detection/process",
                    files={"file": ("test_video.mp4", video_file, "video/mp4")},
                    data={"request": json.dumps({"config": sample_config})},
                    headers=auth_headers
                )
            
            assert response.status_code == 200
            result = response.json()
            assert result["success"] is True
            assert "job_id" in result
            assert result["message"] == "Face detection processing started"
    
    def test_status_endpoint_returns_job_status(self, client, auth_headers):
        """Test the status endpoint returns job status"""
        with patch('main.get_current_user') as mock_auth:
            mock_user = Mock()
            mock_user.id = 1
            mock_auth.return_value = mock_user
            
            # Mock job in the jobs dictionary
            job_id = "test-job-123"
            with patch.dict('main.face_detection_jobs', {
                job_id: {
                    "status": "processing",
                    "progress": 0.5,
                    "created_at": "2024-01-01T00:00:00",
                    "user_id": 1
                }
            }):
                response = client.get(
                    f"/api/face-detection/status/{job_id}",
                    headers=auth_headers
                )
                
                assert response.status_code == 200
                result = response.json()
                assert result["job_id"] == job_id
                assert result["status"] == "processing"
                assert result["progress"] == 0.5
    
    def test_status_endpoint_job_not_found(self, client, auth_headers):
        """Test status endpoint returns 404 for non-existent job"""
        with patch('main.get_current_user') as mock_auth:
            mock_user = Mock()
            mock_user.id = 1
            mock_auth.return_value = mock_user
            
            response = client.get(
                "/api/face-detection/status/non-existent-job",
                headers=auth_headers
            )
            
            assert response.status_code == 404
            assert "Job not found" in response.json()["detail"]
    
    def test_status_endpoint_access_denied(self, client, auth_headers):
        """Test status endpoint denies access to other user's jobs"""
        with patch('main.get_current_user') as mock_auth:
            mock_user = Mock()
            mock_user.id = 1
            mock_auth.return_value = mock_user
            
            # Mock job belonging to different user
            job_id = "test-job-123"
            with patch.dict('main.face_detection_jobs', {
                job_id: {
                    "status": "processing",
                    "progress": 0.5,
                    "created_at": "2024-01-01T00:00:00",
                    "user_id": 2  # Different user
                }
            }):
                response = client.get(
                    f"/api/face-detection/status/{job_id}",
                    headers=auth_headers
                )
                
                assert response.status_code == 403
                assert "Access denied" in response.json()["detail"]
    
    def test_preview_endpoint_generates_preview(self, client, sample_video_file, sample_config, auth_headers):
        """Test the preview endpoint generates preview data"""
        with patch('main.get_current_user') as mock_auth:
            mock_user = Mock()
            mock_user.id = 1
            mock_auth.return_value = mock_user
            
            with patch('main.facial_ai_processor.generate_preview') as mock_preview:
                # Mock preview result
                mock_preview.return_value = {
                    "video_path": sample_video_file,
                    "analysis": {
                        "face_detections": 10,
                        "voice_segments": 2
                    },
                    "preview_frames": [
                        {
                            "timestamp": 1.0,
                            "frame_number": 30,
                            "faces": [{"speaker_id": 0}],
                            "has_speech": True
                        },
                        {
                            "timestamp": 2.0,
                            "frame_number": 60,
                            "faces": [],
                            "has_speech": False
                        }
                    ],
                    "total_frames": 2,
                    "timestamp": "2024-01-01T00:00:00"
                }
                
                with open(sample_video_file, 'rb') as video_file:
                    response = client.post(
                        "/api/face-detection/preview",
                        files={"file": ("test_video.mp4", video_file, "video/mp4")},
                        data={"request": json.dumps({"config": sample_config})},
                        headers=auth_headers
                    )
                
                assert response.status_code == 200
                result = response.json()
                assert result["success"] is True
                assert "preview_data" in result
                assert result["preview_data"]["total_frames"] == 2
                assert len(result["preview_data"]["preview_frames"]) == 2
    
    def test_analyze_endpoint_missing_input(self, client, auth_headers):
        """Test analyze endpoint returns error when no input provided"""
        with patch('main.get_current_user') as mock_auth:
            mock_user = Mock()
            mock_user.id = 1
            mock_auth.return_value = mock_user
            
            response = client.post(
                "/api/face-detection/analyze",
                json={},
                headers=auth_headers
            )
            
            assert response.status_code == 200  # Returns success=False in response
            result = response.json()
            assert result["success"] is False
            assert "Either file upload or video_url is required" in result["error"]
    
    def test_process_endpoint_missing_input(self, client, auth_headers):
        """Test process endpoint returns error when no input provided"""
        with patch('main.get_current_user') as mock_auth:
            mock_user = Mock()
            mock_user.id = 1
            mock_auth.return_value = mock_user
            
            response = client.post(
                "/api/face-detection/process",
                json={},
                headers=auth_headers
            )
            
            assert response.status_code == 200  # Returns success=False in response
            result = response.json()
            assert result["success"] is False
            assert "Either file upload or video_url is required" in result["error"]
    
    def test_preview_endpoint_missing_input(self, client, auth_headers):
        """Test preview endpoint returns error when no input provided"""
        with patch('main.get_current_user') as mock_auth:
            mock_user = Mock()
            mock_user.id = 1
            mock_auth.return_value = mock_user
            
            response = client.post(
                "/api/face-detection/preview",
                json={},
                headers=auth_headers
            )
            
            assert response.status_code == 200  # Returns success=False in response
            result = response.json()
            assert result["success"] is False
            assert "Either file upload or video_url is required" in result["error"]
    
    def test_endpoints_require_authentication(self, client, sample_config):
        """Test that all endpoints require authentication"""
        endpoints = [
            ("/api/face-detection/analyze", {"json": {"config": sample_config}}),
            ("/api/face-detection/process", {"json": {"config": sample_config}}),
            ("/api/face-detection/preview", {"json": {"config": sample_config}}),
            ("/api/face-detection/status/test-job", {"method": "GET"})
        ]
        
        for endpoint, kwargs in endpoints:
            if kwargs.get("method") == "GET":
                response = client.get(endpoint)
            else:
                response = client.post(endpoint, **{k: v for k, v in kwargs.items() if k != "method"})
            
            # Should return 401 or 422 (depending on FastAPI auth setup)
            assert response.status_code in [401, 422]


if __name__ == "__main__":
    # Run the tests
    pytest.main([__file__, "-v"])
