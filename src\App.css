#root {
  /* max-width: 1280px; */
  margin: 0 auto;
  padding: 2rem;
  text-align: center;
}

.logo {
  height: 6em;
  padding: 1.5em;
  will-change: filter;
  transition: filter 300ms;
}
.logo:hover {
  filter: drop-shadow(0 0 2em #646cffaa);
}
.logo.react:hover {
  filter: drop-shadow(0 0 2em #61dafbaa);
}

@keyframes logo-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@media (prefers-reduced-motion: no-preference) {
  a:nth-of-type(2) .logo {
    animation: logo-spin infinite 20s linear;
  }
}

.card {
  padding: 2em;
}

.read-the-docs {
  color: #888;
}
.water-anim-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 50%;
  height: 100%;
  overflow: hidden;
  z-index: 0;
  pointer-events: none;
  transition: left 0.5s cubic-bezier(.65,0,.35,1);
}
.water {
  position: relative;
  width: 100%;
  height: 100%;
  background-color: #80c5de;
  box-shadow: inset 0 0 50px #1c637c;
  clip-path: polygon(0 0, 100% 0, 85% 100%, 15% 100%);
}
.water::before {
  content: "";
  width: 200%;
  height: 200%;
  background-color: #ececec;
  position: absolute;
  top: -150%;
  left: -50%;
  border-radius: 40%;
  animation: anim 12s linear infinite;
}
.water::after {
  content: "";
  width: 204%;
  height: 204%;
  background-color: #ececec80;
  position: absolute;
  top: -150%;
  left: -52%;
  border-radius: 40%;
  animation: anim 12s linear infinite;
  animation-delay: 0.5s;
}
@keyframes anim {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg);}
}


  
  .shadow-3xl {
    box-shadow: 0 22px 54px 0 #7f53ac32, 0 2px 8px 0 #647dee11;
  }
  :root {
    --brand-purple: #7f53ac;
    --brand-blue: #647dee;
  }
  .from-brand-purple { --tw-gradient-from: #7f53ac var(--tw-gradient-from-position); }
  .to-brand-blue     { --tw-gradient-to:   #647dee var(--tw-gradient-to-position); }

  /* Hide all scrollbars (Webkit/Blink/Firefox) for sidebar */
.sidebar, .Sidebar, [data-sidebar] {
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none;  /* IE and Edge */

  /* For Chrome, Safari and Opera */
}
.sidebar::-webkit-scrollbar,
.Sidebar::-webkit-scrollbar,
[data-sidebar]::-webkit-scrollbar {
  display: none;
}

