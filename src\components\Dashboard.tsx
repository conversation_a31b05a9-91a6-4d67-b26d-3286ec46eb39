import React, { ReactNode } from 'react';
import {
  SidebarProvider,
  Sidebar,
  SidebarContent,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuItem,
  SidebarMenuButton,
  SidebarGroup,
  SidebarGroupLabel,
  SidebarGroupContent,
  SidebarFooter,
  SidebarSeparator,
} from '@/components/ui/sidebar';
import { Link } from 'react-router-dom';
import {
  VideoIcon,
  Settings,
  LogOut,
  CreditCard,
  User,
  Users,
  BarChart,
  Play,
  Scissors,
  HelpCircle,
  Home,
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import ThemeToggle from './ThemeToggle';
import { useAuth } from '@/context/AuthContext';
import { Badge } from '@/components/ui/badge';

interface DashboardProps {
  children: ReactNode;
}
const navButton =
  'group flex items-center gap-1 px-2 py-1 my-0.5 rounded font-medium text-[15px] ' +
  'bg-ultra-dark text-white shadow-sm ' +
  'hover:bg-gradient-purple-blue hover:text-white hover:shadow ' +
  'active:scale-95 outline-none select-none';

const sidebarGlass =
  'bg-ultra-dark/95 backdrop-blur border-r border-accent-blue/20 shadow-sm';

const dashboardCard =
  'rounded-xl w-full  bg-ultra-dark/90 shadow-lg border text-sm';

const headerBar =
  'h-11 w-full flex items-center px-4 border-b border-accent-purple/15 bg-ultra-dark/80 shadow-sm';

const Dashboard: React.FC<DashboardProps> = ({ children }) => {
  const { isAuthenticated, logout, user, isAdmin } = useAuth();

  const handleLogout = async () => {
    try {
      await logout();
    } catch (error) {
      console.error(error);
    }
  };

  return (
    <SidebarProvider defaultOpen={true}>
      <div className="flex w-full h-screen  bg-ultra-dark">
        {/* Sidebar */}
        <Sidebar
          className={
            sidebarGlass + ' min-w-[280px] w-[280px] flex flex-col h-full'
          }
          style={{ overflow: 'hidden' }}
        >
          {/* Scrollable Sidebar Content */}
          <div className="flex-1 overflow-y-auto pt-1">
            {/* Sidebar Header (Fixed) */}
            <SidebarHeader>
              <div className="flex items-center mt-14 gap-1.5 px-2 py-2 bg-gradient-purple-blue rounded-md shadow  h-[60px]">
                <VideoIcon className="h-8 w-8 text-white ml-1" />
                <span className="font-black text-lg text-white ml-2">
                  SmartClips
                </span>
              </div>

              {isAuthenticated && user && (
                <Link
                  to="/profile"
                  className={
                    navButton +
                    ' !px-2 !py-1.5 !rounded-lg !gap-2 w-full transition transform-gpu duration-150 ' +
                    'hover:[&>*]:text-white active:scale-95'
                  }
                  style={{
                    minHeight: 56,
                    display: 'flex',
                    alignItems: 'center',
                  }}
                >
                  <User className="h-8 w-8 text-accent-blue" />
                  <div className="flex flex-col min-w-0">
                    <span className="font-semibold text-accent-blue text-xl truncate">
                      {user.username}
                    </span>
                    <span className="text-[16px] text-accent-purple/80">
                      {isAdmin ? 'Admin' : 'Creator'}
                    </span>
                  </div>
                </Link>
              )}
            </SidebarHeader>
            <SidebarGroup>
              <SidebarGroupLabel className="px-2 pb-0.5 text-[11px] text-accent-blue/80 uppercase font-semibold tracking-wide">
                Navigation
              </SidebarGroupLabel>
              <SidebarGroupContent>
                <SidebarMenu>
                  <SidebarMenuItem>
                    <SidebarMenuButton asChild tooltip="Home">
                      <Link to="/" className={navButton}>
                        <Home className="mr-1 h-4 w-4" />
                        Home
                      </Link>
                    </SidebarMenuButton>
                  </SidebarMenuItem>
                  {isAuthenticated && (
                    <SidebarMenuItem>
                      <SidebarMenuButton asChild tooltip="Credits">
                        <Link to="/credits" className={navButton}>
                          <CreditCard className="mr-1 h-4 w-4" />
                          Credits
                          <Badge className="ml-auto bg-ultra-dark/60 text-white border border-accent-blue/40 text-[10px] px-1">
                            {user?.credits || 0}
                          </Badge>
                        </Link>
                      </SidebarMenuButton>
                    </SidebarMenuItem>
                  )}
                </SidebarMenu>
              </SidebarGroupContent>
            </SidebarGroup>

            <SidebarSeparator className="my-0.5 border-accent-purple/10" />

            <SidebarGroup>
              <SidebarGroupLabel className="px-2 text-[11px] text-accent-purple/70 uppercase font-semibold tracking-wide">
                Create
              </SidebarGroupLabel>
              <SidebarGroupContent>
                <SidebarMenu>
                  <SidebarMenuItem>
                    <SidebarMenuButton asChild tooltip="Smart Clipper">
                      <Link to="/smart-clipper" className={navButton}>
                        <Scissors className="mr-1 h-4 w-4" />
                        Smart Clipper
                      </Link>
                    </SidebarMenuButton>
                  </SidebarMenuItem>
                  {isAuthenticated && (
                    <SidebarMenuItem>
                      <SidebarMenuButton asChild tooltip="My Clips">
                        <Link to="/clip-results" className={navButton}>
                          <Play className="mr-1 h-4 w-4" />
                          My Clips
                        </Link>
                      </SidebarMenuButton>
                    </SidebarMenuItem>
                  )}
                </SidebarMenu>
              </SidebarGroupContent>
            </SidebarGroup>

            <SidebarSeparator className="border-accent-blue/10" />

            {isAdmin && (
              <SidebarGroup>
                <SidebarGroupLabel className="px-2 text-[11px] text-accent-blue/70 uppercase font-semibold tracking-wide">
                  Admin
                </SidebarGroupLabel>
                <SidebarGroupContent>
                  <SidebarMenu>
                    <SidebarMenuItem>
                      <SidebarMenuButton asChild tooltip="Users">
                        <Link to="/admin/users" className={navButton}>
                          <Users className="mr-1 h-4 w-4" />
                          Users
                        </Link>
                      </SidebarMenuButton>
                    </SidebarMenuItem>
                    <SidebarMenuItem>
                      <SidebarMenuButton asChild tooltip="Analytics">
                        <Link to="/admin/analytics" className={navButton}>
                          <BarChart className="mr-1 h-4 w-4" />
                          Analytics
                        </Link>
                      </SidebarMenuButton>
                    </SidebarMenuItem>
                  </SidebarMenu>
                </SidebarGroupContent>
              </SidebarGroup>
            )}

            <SidebarSeparator className="border-accent-blue/10" />

            <SidebarGroup>
              <SidebarGroupLabel className="px-2 text-[11px] text-accent-purple/55 uppercase font-semibold tracking-wide">
                Support
              </SidebarGroupLabel>
              <SidebarGroupContent>
                <SidebarMenu>
                  {isAuthenticated && (
                    <SidebarMenuItem>
                      <SidebarMenuButton asChild tooltip="Support">
                        <Link to="/support" className={navButton}>
                          <HelpCircle className="mr-1 h-4 w-4" />
                          Support
                        </Link>
                      </SidebarMenuButton>
                    </SidebarMenuItem>
                  )}
                  {!isAuthenticated && (
                    <>
                      <SidebarMenuItem>
                        <SidebarMenuButton asChild tooltip="Login">
                          <Link to="/login" className={navButton}>
                            <User className="mr-1 h-4 w-4" />
                            Login
                          </Link>
                        </SidebarMenuButton>
                      </SidebarMenuItem>
                      <SidebarMenuItem>
                        <SidebarMenuButton asChild tooltip="Register">
                          <Link to="/register" className={navButton}>
                            <User className="mr-1 h-4 w-4" />
                            Register
                          </Link>
                        </SidebarMenuButton>
                      </SidebarMenuItem>
                    </>
                  )}
                </SidebarMenu>
              </SidebarGroupContent>
            </SidebarGroup>
          </div>

          {/* Sidebar Footer (Fixed) */}
          <SidebarFooter className="border-t border-accent-blue/15 bg-ultra-dark/95 shadow flex flex-col gap-0.5 rounded-t">
            <ThemeToggle />
            {isAuthenticated && (
              <SidebarGroup>
                <SidebarGroupContent>
                  <SidebarMenu>
                    <SidebarMenuItem>
                      <SidebarMenuButton asChild tooltip="Profile">
                        <Link to="/profile" className={navButton}>
                          <User className="mr-1 h-4 w-4" />
                          <span className="truncate">
                            {user?.username || 'Profile'}
                          </span>
                        </Link>
                      </SidebarMenuButton>
                    </SidebarMenuItem>
                    <SidebarMenuItem>
                      <SidebarMenuButton asChild tooltip="Settings">
                        <Link to="/settings" className={navButton}>
                          <Settings className="mr-1 h-4 w-4" />
                          Settings
                        </Link>
                      </SidebarMenuButton>
                    </SidebarMenuItem>
                    <SidebarMenuItem>
                      <SidebarMenuButton asChild>
                        <Button
                          variant="ghost"
                          className={
                            navButton + ' !bg-gradient-purple-blue !text-white'
                          }
                          onClick={handleLogout}
                        >
                          <LogOut className="mr-1 h-4 w-4" />
                          Log out
                        </Button>
                      </SidebarMenuButton>
                    </SidebarMenuItem>
                  </SidebarMenu>
                </SidebarGroupContent>
              </SidebarGroup>
            )}
          </SidebarFooter>
        </Sidebar>

        {/* Main Content Area */}
        <div className=" flex flex-col bg-ultra-dark mt-10 w-full">
          {/* <header className={headerBar}>
            <span className="text-lg font-black text-white tracking-wide h-[42px]">
              Dashboard
            </span>
          </header> */}
          <main className=" py-4 px-2 flex flex-col gap-3 items-center bg-ultra-dark">
            <div>{children}</div>
          </main>
        </div>
      </div>
    </SidebarProvider>
  );
};

export default Dashboard;
