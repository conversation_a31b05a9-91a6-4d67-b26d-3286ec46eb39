#!/usr/bin/env python3
"""
Simple demo script to process videoplayback (2).mp4 with enhanced facial AI
"""

import os
import sys
import time
import logging

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def main():
    print("🚀 Enhanced Facial AI Demo")
    print("=" * 40)
    
    # Check if video exists
    video_path = "videos/videoplayback (2).mp4"
    if not os.path.exists(video_path):
        print(f"❌ Video not found: {video_path}")
        return
    
    print(f"📹 Input video: {video_path}")
    
    # Get video info
    file_size = os.path.getsize(video_path) / (1024 * 1024)  # MB
    print(f"📊 File size: {file_size:.2f} MB")
    
    try:
        # Import the enhanced processor
        from facial_ai_processor import FacialAIProcessor, ZoomConfig
        print("✅ Enhanced facial AI processor imported successfully")
        
        # Initialize processor
        processor = FacialAIProcessor()
        print("✅ Processor initialized")
        
        # Create enhanced configuration
        config = ZoomConfig(
            zoom_level=2.0,
            include_torso=True,
            headroom_ratio=0.15,
            torso_ratio=0.6,
            speaker_switch_threshold=0.5,
            transition_smoothness=0.4,
            detection_sensitivity=0.7,
            voice_threshold=0.3
        )
        
        print("\n⚙️ Enhanced Configuration:")
        print(f"  🔍 Zoom Level: {config.zoom_level}x")
        print(f"  👤 Include Torso: {config.include_torso}")
        print(f"  📏 Headroom Ratio: {config.headroom_ratio}")
        print(f"  🎯 Speaker Switch Threshold: {config.speaker_switch_threshold}s")
        print(f"  🌊 Transition Smoothness: {config.transition_smoothness}")
        
        # Output path
        output_path = "videos/enhanced_videoplayback_2_demo.mp4"
        print(f"\n📹 Output video: {output_path}")
        
        print("\n🎬 Starting enhanced processing...")
        start_time = time.time()
        
        # Process the video
        result = processor.generate_complete_processed_video(
            video_path,
            output_path,
            config
        )
        
        processing_time = time.time() - start_time
        
        if result["success"]:
            print("\n✅ Enhanced processing completed successfully!")
            print(f"⏱️ Total processing time: {processing_time:.2f} seconds")
            
            # Check output file
            if os.path.exists(output_path):
                output_size = os.path.getsize(output_path) / (1024 * 1024)  # MB
                print(f"📊 Output file size: {output_size:.2f} MB")
                
                # Display analysis results
                analysis = result.get("analysis", {})
                print(f"\n📊 Processing Analysis:")
                print(f"  🎭 Speaker segments detected: {analysis.get('speaker_segments', 0)}")
                print(f"  🎯 Zoom effects applied: {analysis.get('zoom_segments', 0)}")
                print(f"  👥 Face detections: {analysis.get('face_detections', 0)}")
                print(f"  🗣️ Voice segments: {analysis.get('voice_segments', 0)}")
                
                # Display enhancements
                enhancements = result.get("enhancements", {})
                print(f"\n🔧 Applied Enhancements:")
                for enhancement, enabled in enhancements.items():
                    status = "✅" if enabled else "❌"
                    print(f"  {status} {enhancement.replace('_', ' ').title()}")
                
                print(f"\n🎉 Demo completed successfully!")
                print(f"📁 Enhanced video saved to: {output_path}")
                print(f"\n💡 You can now compare:")
                print(f"  📹 Original: {video_path}")
                print(f"  🎬 Enhanced: {output_path}")
                
            else:
                print("❌ Output file was not created")
                
        else:
            print(f"❌ Processing failed: {result.get('error', 'Unknown error')}")
            
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("Make sure all dependencies are installed")
    except Exception as e:
        print(f"❌ Error during processing: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
