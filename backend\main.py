import asyncio
from openai import AsyncOpenAI
import url_processor
import storage
from advanced_video_processor import AdvancedVideoProcessor, process_video_with_enhancements
import video_processing
from facial_ai_processor import FacialAIProcessor, ZoomConfig
import models
from database import SessionLocal, engine, Base
import time
from fastapi.staticfiles import StaticFiles
import requests
from google.cloud import texttospeech
import openai
from moviepy.editor import VideoFileClip
import cloudinary.uploader
import cloudinary
from sqlalchemy.orm import Session
from pydantic import BaseModel
from passlib.context import CryptContext
from jose import JWTError, jwt
from fastapi.security import OAuth2PasswordBearer, OAuth2PasswordRequestForm
from fastapi.middleware.cors import CORSMiddleware
from fastapi import FastAPI, Depends, HTTPException, status, File, UploadFile, Form, BackgroundTasks
import json
from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any
import subprocess
import logging
import shutil
import os
import uvicorn
import ffmpeg
import uuid

import moviepy.config as mpconfig
# mpconfig.change_settings(
#     {"IMAGEMAGICK_BINARY": r"C:\Program Files\ImageMagick-7.1.2-Q16-HDRI\magick.exe"})

# for live
IMAGEMAGICK_BINARY = os.getenv('IMAGEMAGICK_BINARY', '/usr/bin/convert')
mpconfig.change_settings({"IMAGEMAGICK_BINARY": IMAGEMAGICK_BINARY})


os.environ["TOKENIZERS_PARALLELISM"] = "false"


# Configure FFmpeg path BEFORE importing any MoviePy modules


def find_ffmpeg():
    # First try to find ffmpeg in PATH
    try:
        # for macOS/Linux - `which`, and for Windows - `where`
        cmd = 'where' if os.name == 'nt' else 'which'
        result = subprocess.run(
            [cmd, 'ffmpeg'], capture_output=True, text=True)
        if result.returncode == 0:
            ffmpeg_path = result.stdout.strip().split('\n')[0]
            return ffmpeg_path
    except:
        pass

    # Fallback common paths (only for Windows)
    if os.name == 'nt':
        common_paths = [
            r"C:\ffmpeg\bin\ffmpeg.exe",
            r"C:\ffmpeg\ffmpeg-7.1.1-essentials_build\bin\ffmpeg.exe",
            r"C:\Program Files\ffmpeg\bin\ffmpeg.exe",
            r"C:\Program Files (x86)\ffmpeg\bin\ffmpeg.exe"
        ]

        for path in common_paths:
            if os.path.exists(path):
                return path

    return None


# Set FFmpeg path as environment variable before importing MoviePy
ffmpeg_path = find_ffmpeg()
if ffmpeg_path:
    os.environ['FFMPEG_BINARY'] = ffmpeg_path
    print(f"Using FFmpeg at: {ffmpeg_path}")
else:
    print("Warning: FFmpeg not found in system PATH or common locations")

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize Google Cloud TTS Client (lazy initialization)
tts_client = None


def get_tts_client():
    global tts_client
    if tts_client is None:
        try:
            tts_client = texttospeech.TextToSpeechClient()
        except Exception as e:
            logger.warning(
                f"Failed to initialize Google Cloud TTS client: {e}")
            tts_client = False
    return tts_client if tts_client is not False else None


# Initialize the app
app = FastAPI(title="QuikClips API",
              description="Backend API for QuikClips video processing")

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # For production, replace with specific origins
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.mount("/static", StaticFiles(directory="static"), name="static")


# Create tables
Base.metadata.create_all(bind=engine)

# Directory for temporary files
TEMP_DIR = "temp"
os.makedirs(TEMP_DIR, exist_ok=True)

# Authentication configuration
SECRET_KEY = os.getenv("SECRET_KEY", "development_secret_key")
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 60 * 24  # 24 hours

# Password hashing
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="token")

# Initialize Cloudinary
cloudinary.config(
    cloud_name=os.getenv("CLOUDINARY_CLOUD_NAME"),
    api_key=os.getenv("CLOUDINARY_API_KEY"),
    api_secret=os.getenv("CLOUDINARY_API_SECRET")
)

# Initialize S3 storage if configured
s3_enabled = os.getenv("AWS_ACCESS_KEY_ID") and os.getenv(
    "AWS_SECRET_ACCESS_KEY")
if s3_enabled:
    storage.initialize_s3()

# --- Authentication Models ---


class Token(BaseModel):
    access_token: str
    token_type: str
    user_id: int
    username: str
    subscription: str


class TokenData(BaseModel):
    username: Optional[str] = None


class UserCreate(BaseModel):
    username: str
    email: str
    password: str


class UserProfile(BaseModel):
    username: str
    email: str
    avatar_url: Optional[str] = None
    bio: Optional[str] = None
    subscription: str = "free"

# --- Video Processing Models ---


class VideoSegment(BaseModel):
    start_time: float
    end_time: float
    text: str


class ViralityAnalysisResult(BaseModel):
    score: int
    feedback: str


class ProcessedVideo(BaseModel):
    segments: List[VideoSegment]
    video_urls: List[str]
    # virality_result: Optional[Dict[str, str]] = None
    virality_analysis: Optional[ViralityAnalysisResult] = None


class URLProcessRequest(BaseModel):
    url: str
    min_duration: float = 10.0
    max_duration: float = 60.0
    quality: str = "best"
    max_clips: Optional[int] = 10  # Default to 10 clips
    analyze_virality: Optional[bool] = False,
    platform: Optional[str] = None
    subscription: Optional[str] = None


class URLValidationResponse(BaseModel):
    valid: bool
    platform: Optional[str] = None
    video_id: Optional[str] = None
    error: Optional[str] = None


class VideoMetadataResponse(BaseModel):
    title: str
    duration: float
    uploader: str
    view_count: int
    platform: str
    thumbnail: str

# Advanced Video Processing Models


class AdvancedProcessingOptions(BaseModel):
    add_subtitles: bool = True
    add_emojis: bool = True
    add_clipart: bool = True
    create_short_form: bool = True
    platforms: List[str] = ["tiktok", "instagram"]
    subtitle_style: str = "modern"  # modern, tiktok, elegant
    max_short_clips: int = 3
    max_duration_seconds: Optional[float] = None


class AdvancedProcessingRequest(BaseModel):
    video_url: Optional[str] = None  # For URL processing
    options: AdvancedProcessingOptions = AdvancedProcessingOptions()


class AdvancedProcessingResponse(BaseModel):
    success: bool
    message: str
    original_video: str
    processed_videos: Dict[str, str] = {}
    short_form_clips: Dict[str, List[str]] = {}
    metadata: Dict[str, Any] = {}
    processing_time: float = 0.0
    error: Optional[str] = None


class ProcessedClipDetail(BaseModel):
    url: str
    text: str
    start_time: float
    end_time: float
    virality_analysis: Optional[ViralityAnalysisResult] = None,
    platform: Optional[str] = None


class InstantProcessResponse(BaseModel):
    """Response for the instant processing endpoint."""
    success: bool
    message: str
    clips: List[ProcessedClipDetail] = []
    error: Optional[str] = None


class InstantProcessOptions(BaseModel):
    """Options for the one-click instant processing."""
    add_emojis: bool = False
    max_clips: int = 10
    subtitle_style: str = "clean_modern"
    platform: str = "tiktok"


class InstantProcessURLRequest(BaseModel):
    """Request model for instant processing from a URL."""
    url: str
    options: InstantProcessOptions = InstantProcessOptions()


# --- Helper Functions ---


def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


def verify_password(plain_password, hashed_password):
    return pwd_context.verify(plain_password, hashed_password)


def get_password_hash(password):
    return pwd_context.hash(password)


def get_user(db, username: str):
    return db.query(models.User).filter(models.User.username == username).first()


def authenticate_user(db, username: str, password: str):
    user = get_user(db, username)
    if not user or not verify_password(password, user.hashed_password):
        return False
    return user


def create_access_token(data: dict, expires_delta: Optional[timedelta] = None):
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=15)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt


async def get_current_user(token: str = Depends(oauth2_scheme), db: Session = Depends(get_db)):
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        username: str = payload.get("sub")
        if username is None:
            raise credentials_exception
        token_data = TokenData(username=username)
    except JWTError:
        raise credentials_exception
    user = get_user(db, username=token_data.username)
    if user is None:
        raise credentials_exception
    return user

# Initialize OpenAI client (lazy initialization)
openai_client = None


# def get_openai_client():
#     global openai_client
#     if openai_client is None:
#         try:
#             openai_client = AsyncOpenAI()
#         except Exception as e:
#             logger.warning(f"Failed to initialize OpenAI client: {e}")
#             openai_client = False
#     return openai_client if openai_client is not False else None


async def analyze_virality_with_openai(transcript: str) -> Optional[Dict[str, Any]]:
    prompt = (
        f"You are an encouraging and insightful social media strategist. Your goal is to analyze a video transcript, "
        f"identify its strengths, and provide constructive, positive feedback to boost its viral potential.\n\n"
        f"When providing a score, be optimistic. A score of 50-60 should indicate a solid foundation with potential, not a failure. "
        f"Scores below 40 should be reserved for content with fundamental issues, but the feedback must still be encouraging.\n\n"
        f"You MUST respond with a valid JSON object containing two keys: "
        f"1. 'score': An integer from 0 to 100, reflecting the content's potential with an optimistic lens. "
        f"2. 'feedback': A string containing a few bullet points. Start by highlighting what's already working well, "
        f"then suggest 'opportunities' or 'ideas to try' to make it even more engaging. Use markdown for bullets (e.g., '* Point 1\\n* Point 2'). "
        f"Do not include any text outside of the JSON object.\n\n"
        f"Transcript to Analyze:\n{transcript[:3000]}"
    )

    try:
        client = AsyncOpenAI()
        if not client:
            logger.warning("OpenAI client not available")
            return None

        response = await client.chat.completions.create(
            model="gpt-4-turbo-preview",
            messages=[
                # More explicit system prompt
                {"role": "system", "content": "You are a helpful assistant that analyzes text and responds ONLY with a valid, complete JSON object. Do not write any other text."},
                {"role": "user", "content": prompt}
            ],
            # INCREASED TOKENS to prevent cutoff
            max_tokens=400,
            temperature=0.4,
            response_format={"type": "json_object"},
        )

        result_string = response.choices[0].message.content.strip()
        logger.info(f"Raw virality analysis from OpenAI: '{result_string}'")

        try:
            parsed_data = json.loads(result_string)
            return parsed_data
        except json.JSONDecodeError:
            logger.error(f"Failed to decode JSON from OpenAI: {result_string}")
            return None

    except Exception as e:
        logger.error(f"OpenAI virality analysis API call failed: {e}")
        return None

# # --- Routes ---


@app.get("/")
async def root():
    return {"message": "SmartClips Backend API is running!", "status": "ok", "version": "1.0.0"}


@app.get("/health")
async def health_check():
    return {
        "status": "healthy",
        "ffmpeg_available": os.path.exists(os.environ.get('FFMPEG_BINARY', 'ffmpeg')),
        "database": "connected",
        "services": {
            "openai": bool(os.getenv("OPENAI_API_KEY")),
            "cloudinary": bool(os.getenv("CLOUDINARY_API_KEY")),
            "elevenlabs": bool(os.getenv("ELEVENLABS_API_KEY"))
        }
    }


@app.post("/token", response_model=Token)
async def login_for_access_token(form_data: OAuth2PasswordRequestForm = Depends(), db: Session = Depends(get_db)):
    user = authenticate_user(db, form_data.username, form_data.password)

    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password",
            headers={"WWW-Authenticate": "Bearer"},
        )
    access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={"sub": user.username}, expires_delta=access_token_expires
    )
    return {
        "access_token": access_token,
        "token_type": "bearer",
        "user_id": user.id,
        "username": user.username,
        "subscription": user.subscription
    }


@app.post("/users/", response_model=UserProfile)
async def create_user(user: UserCreate, db: Session = Depends(get_db)):
    db_user = get_user(db, username=user.username)
    if db_user:
        raise HTTPException(
            status_code=400, detail="Username already registered")

    email_exists = db.query(models.User).filter(
        models.User.email == user.email).first()
    if email_exists:
        raise HTTPException(status_code=400, detail="Email already registered")

    hashed_password = get_password_hash(user.password)
    db_user = models.User(
        username=user.username,
        email=user.email,
        hashed_password=hashed_password,
        subscription="free"
    )
    db.add(db_user)
    db.commit()
    db.refresh(db_user)

    return {
        "username": db_user.username,
        "email": db_user.email,
        "avatar_url": db_user.avatar_url,
        "bio": db_user.bio,
        "subscription": db_user.subscription
    }


@app.get("/users/me/", response_model=UserProfile)
async def read_users_me(current_user: models.User = Depends(get_current_user)):
    return {
        "username": current_user.username,
        "email": current_user.email,
        "avatar_url": current_user.avatar_url,
        "bio": current_user.bio,
        "subscription": current_user.subscription
    }


@app.post("/upload", response_model=ProcessedVideo)
async def upload_video(
    background_tasks: BackgroundTasks,
    file: UploadFile = File(...),
    storage_type: str = Form("cloudinary"),
    min_duration: float = Form(10.0),
    max_duration: float = Form(60.0),
    current_user: models.User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    # Check subscription limits
    if current_user.subscription == "free" and db.query(models.Video).filter(models.Video.user_id == current_user.id).count() >= 5:
        raise HTTPException(
            status_code=402, detail="Free subscription limit reached (5 videos)")

    try:
        # Save the uploaded video temporarily
        temp_video_path = os.path.join(TEMP_DIR, file.filename)
        with open(temp_video_path, "wb") as temp_video:
            shutil.copyfileobj(file.file, temp_video)

        # Log video info
        try:
            with VideoFileClip(temp_video_path) as clip:
                duration = clip.duration
                resolution = f"{clip.size[0]}x{clip.size[1]}"
        except Exception as e:
            logger.error(f"Error getting video info: {str(e)}")
            duration = 0
            resolution = "unknown"

        # Create video record
        db_video = models.Video(
            user_id=current_user.id,
            filename=file.filename,
            original_path=temp_video_path,
            duration=duration,
            resolution=resolution,
            status="processing"
        )
        db.add(db_video)
        db.commit()
        db.refresh(db_video)

        # Process the video
        transcript, timestamps = video_processing.transcribe_video(
            temp_video_path, TEMP_DIR)
        segments = video_processing.segment_transcript(
            transcript,
            timestamps,
            min_duration=min_duration,
            max_duration=max_duration,
            refine_with_ai=current_user.subscription != "free"
        )
        clipped_videos = video_processing.clip_video_from_text(
            temp_video_path, segments, TEMP_DIR)

        # Upload to appropriate storage
        video_urls = []
        for clip_path in clipped_videos:
            if storage_type == "s3" and s3_enabled:
                url = storage.upload_to_s3(clip_path)
            else:
                url = storage.upload_to_cloudinary(clip_path)
            video_urls.append(url)

            # Create clip record with proper duration calculation
            try:
                with VideoFileClip(clip_path) as clip:
                    clip_duration = clip.duration
            except Exception as e:
                logger.error(
                    f"Error getting clip duration for {clip_path}: {str(e)}")
                clip_duration = 0

            db_clip = models.VideoClip(
                video_id=db_video.id,
                url=url,
                duration=clip_duration,
                status="completed"
            )
            db.add(db_clip)

        # Update video status
        db_video.status = "completed"
        db_video.processed_count = len(clipped_videos)
        db.commit()

        # Clean up in background
        def cleanup():
            try:
                os.remove(temp_video_path)
                for clip in clipped_videos:
                    if os.path.exists(clip):
                        os.remove(clip)
            except Exception as e:
                logger.error(f"Cleanup error: {str(e)}")

        background_tasks.add_task(cleanup)

        return {
            "segments": [
                {"start_time": s["start"],
                    "end_time": s["end"], "text": s["text"]}
                for s in segments
            ],
            "video_urls": video_urls
        }

    except Exception as e:
        logger.error(f"Upload error: {str(e)}")
        if 'db_video' in locals():
            db_video.status = "failed"
            db_video.error_message = str(e)
            db.commit()
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/edit/trim")
async def trim_video(
    file: UploadFile = File(...),
    start_time: float = Form(...),
    end_time: float = Form(...),
    current_user: models.User = Depends(get_current_user)
):
    try:
        # Save the uploaded video temporarily
        temp_video_path = os.path.join(TEMP_DIR, file.filename)
        with open(temp_video_path, "wb") as temp_video:
            shutil.copyfileobj(file.file, temp_video)

        # Trim the video
        output_path = os.path.join(TEMP_DIR, f"trimmed_{file.filename}")
        video_processing.trim_video(
            temp_video_path, output_path, start_time, end_time)

        # Upload to Cloudinary
        url = storage.upload_to_cloudinary(output_path)

        # Cleanup
        os.remove(temp_video_path)
        os.remove(output_path)

        return {"url": url}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/edit/speed")
async def adjust_speed(
    file: UploadFile = File(...),
    speed_factor: float = Form(...),
    current_user: models.User = Depends(get_current_user)
):
    try:
        # Save the uploaded video temporarily
        temp_video_path = os.path.join(TEMP_DIR, file.filename)
        with open(temp_video_path, "wb") as temp_video:
            shutil.copyfileobj(file.file, temp_video)

        # Adjust speed
        output_path = os.path.join(TEMP_DIR, f"speed_{file.filename}")
        video_processing.adjust_speed(
            temp_video_path, output_path, speed_factor)

        # Upload to Cloudinary
        url = storage.upload_to_cloudinary(output_path)

        # Cleanup
        os.remove(temp_video_path)
        os.remove(output_path)

        return {"url": url}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/edit/crop")
async def crop_video(
    file: UploadFile = File(...),
    x1: int = Form(...),
    y1: int = Form(...),
    x2: int = Form(...),
    y2: int = Form(...),
    current_user: models.User = Depends(get_current_user)
):
    try:
        # Save the uploaded video temporarily
        temp_video_path = os.path.join(TEMP_DIR, file.filename)
        with open(temp_video_path, "wb") as temp_video:
            shutil.copyfileobj(file.file, temp_video)

        # Crop the video
        output_path = os.path.join(TEMP_DIR, f"cropped_{file.filename}")
        video_processing.crop_video(
            temp_video_path, output_path, x1, y1, x2, y2)

        # Upload to Cloudinary
        url = storage.upload_to_cloudinary(output_path)

        # Cleanup
        os.remove(temp_video_path)
        os.remove(output_path)

        return {"url": url}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/edit/rotate")
async def rotate_video(
    file: UploadFile = File(...),
    angle: int = Form(...),
    current_user: models.User = Depends(get_current_user)
):
    try:
        # Save the uploaded video temporarily
        temp_video_path = os.path.join(TEMP_DIR, file.filename)
        with open(temp_video_path, "wb") as temp_video:
            shutil.copyfileobj(file.file, temp_video)

        # Rotate the video
        output_path = os.path.join(TEMP_DIR, f"rotated_{file.filename}")
        video_processing.rotate_video(temp_video_path, output_path, angle)

        # Upload to Cloudinary
        url = storage.upload_to_cloudinary(output_path)

        # Cleanup
        os.remove(temp_video_path)
        os.remove(output_path)

        return {"url": url}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/edit/merge")
async def merge_videos(
    files: List[UploadFile] = File(...),
    current_user: models.User = Depends(get_current_user)
):
    try:
        temp_paths = []
        for file in files:
            temp_path = os.path.join(TEMP_DIR, file.filename)
            with open(temp_path, "wb") as temp_video:
                shutil.copyfileobj(file.file, temp_video)
            temp_paths.append(temp_path)

        # Merge the videos
        output_path = os.path.join(
            TEMP_DIR, f"merged_{datetime.now().timestamp()}.mp4")
        video_processing.merge_videos(temp_paths, output_path)

        # Upload to Cloudinary
        url = storage.upload_to_cloudinary(output_path)

        # Cleanup
        for path in temp_paths:
            os.remove(path)
        os.remove(output_path)

        return {"url": url}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/videos/")
async def list_videos(
    skip: int = 0,
    limit: int = 20,
    current_user: models.User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    videos = db.query(models.Video).filter(
        models.Video.user_id == current_user.id
    ).offset(skip).limit(limit).all()

    result = []
    for video in videos:
        clips = db.query(models.VideoClip).filter(
            models.VideoClip.video_id == video.id).all()
        result.append({
            "id": video.id,
            "filename": video.filename,
            "duration": video.duration,
            "created_at": video.created_at,
            "status": video.status,
            "clips": [{"id": clip.id, "url": clip.url, "duration": clip.duration} for clip in clips]
        })

    return result


@app.get("/user/clips")
async def get_user_clips(
    skip: int = 0,
    limit: int = 20,
    current_user: models.User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get user's clips with enhanced metadata for dashboard"""
    clips = db.query(models.VideoClip).join(models.Video).filter(
        models.Video.user_id == current_user.id
    ).offset(skip).limit(limit).all()

    result = []
    for clip in clips:
        # Get video metadata
        video = db.query(models.Video).filter(
            models.Video.id == clip.video_id).first()

        result.append({
            "id": clip.id,
            "url": clip.url,
            "title": f"{video.filename} - Clip {clip.id}" if video else f"Clip {clip.id}",
            "thumbnail": clip.url,  # TODO: Generate actual thumbnails
            "duration": clip.duration,
            "views": 0,  # TODO: Implement view tracking
            "likes": 0,  # TODO: Implement like tracking
            "comments": 0,  # TODO: Implement comment tracking
            "vitalityScore": 75,  # TODO: Calculate actual virality score
            "status": "published",  # TODO: Implement status tracking
            "createdAt": clip.created_at.strftime("%Y-%m-%d") if clip.created_at else None,
            "platform": "SmartClips",  # TODO: Track actual platform
            "videoId": clip.video_id
        })

    return result


@app.get("/user/stats")
async def get_user_stats(
    current_user: models.User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get user statistics for dashboard"""
    # Count user's videos and clips
    video_count = db.query(models.Video).filter(
        models.Video.user_id == current_user.id).count()
    clip_count = db.query(models.VideoClip).join(models.Video).filter(
        models.Video.user_id == current_user.id
    ).count()

    # TODO: Implement actual view tracking, subscriber tracking, etc.
    return {
        "totalViews": clip_count * 1500,  # Mock calculation
        "totalVideos": video_count,
        "totalClips": clip_count,
        "totalSubscribers": 0,  # TODO: Implement subscriber tracking
        "watchTime": clip_count * 45,  # Mock calculation in minutes
        "credits": current_user.credits or 0
    }





@app.get("/user/video-count")
async def get_user_video_count(
    current_user: models.User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get user's video and clip counts"""
    video_count = db.query(models.Video).filter(
        models.Video.user_id == current_user.id).count()
    clip_count = db.query(models.VideoClip).join(models.Video).filter(
        models.Video.user_id == current_user.id
    ).count()

    return {
        "videos": video_count,
        "clips": clip_count
    }


# --- Manual Editor API Endpoints ---

class EditorProject(BaseModel):
    id: Optional[str] = None
    name: str
    description: Optional[str] = None
    timeline_data: Dict[str, Any]
    clips: List[Dict[str, Any]]
    text_overlays: List[Dict[str, Any]] = []
    effects: List[Dict[str, Any]] = []
    created_at: Optional[str] = None
    updated_at: Optional[str] = None


class TimelineClip(BaseModel):
    id: str
    start: float
    end: float
    duration: float
    url: str
    title: str
    track: int


class TextOverlay(BaseModel):
    id: str
    text: str
    x: float
    y: float
    fontSize: int
    color: str
    startTime: float
    endTime: float


@app.post("/editor/projects")
async def create_editor_project(
    project: EditorProject,
    current_user: models.User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Create a new editor project"""
    try:
        # TODO: Implement actual database storage for editor projects
        # For now, return mock response
        project_id = f"project_{int(datetime.now().timestamp())}"

        return {
            "id": project_id,
            "name": project.name,
            "description": project.description,
            "timeline_data": project.timeline_data,
            "clips": project.clips,
            "text_overlays": project.text_overlays,
            "effects": project.effects,
            "created_at": datetime.now().isoformat(),
            "updated_at": datetime.now().isoformat(),
            "user_id": current_user.id
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/editor/projects")
async def get_editor_projects(
    current_user: models.User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get all editor projects for the current user"""
    try:
        # TODO: Implement actual database query for editor projects
        # For now, return mock data
        return [
            {
                "id": "project_1",
                "name": "My First Project",
                "description": "A sample video project",
                "created_at": "2024-01-15T10:00:00Z",
                "updated_at": "2024-01-15T10:30:00Z",
                "clip_count": 3,
                "duration": 45.5
            }
        ]
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/editor/projects/{project_id}")
async def get_editor_project(
    project_id: str,
    current_user: models.User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get a specific editor project"""
    try:
        # TODO: Implement actual database query for specific project
        # For now, return mock data
        return {
            "id": project_id,
            "name": "My Video Project",
            "description": "A comprehensive video editing project",
            "timeline_data": {
                "duration": 60.0,
                "tracks": 2
            },
            "clips": [
                {
                    "id": "clip_1",
                    "start": 0,
                    "end": 30,
                    "duration": 30,
                    "url": "https://example.com/video1.mp4",
                    "title": "Intro Clip",
                    "track": 0
                }
            ],
            "text_overlays": [],
            "effects": [],
            "created_at": "2024-01-15T10:00:00Z",
            "updated_at": "2024-01-15T10:30:00Z"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.put("/editor/projects/{project_id}")
async def update_editor_project(
    project_id: str,
    project: EditorProject,
    current_user: models.User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Update an existing editor project"""
    try:
        # TODO: Implement actual database update for editor projects
        # For now, return mock response
        return {
            "id": project_id,
            "name": project.name,
            "description": project.description,
            "timeline_data": project.timeline_data,
            "clips": project.clips,
            "text_overlays": project.text_overlays,
            "effects": project.effects,
            "updated_at": datetime.now().isoformat(),
            "user_id": current_user.id
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.delete("/editor/projects/{project_id}")
async def delete_editor_project(
    project_id: str,
    current_user: models.User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Delete an editor project"""
    try:
        # TODO: Implement actual database deletion for editor projects
        return {"message": "Project deleted successfully"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/editor/projects/{project_id}/save")
async def save_editor_project(
    project_id: str,
    save_data: Dict[str, Any],
    current_user: models.User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Save editor project state"""
    try:
        # TODO: Implement actual save functionality
        # This would save the current timeline state, clips, overlays, etc.

        return {
            "message": "Project saved successfully",
            "project_id": project_id,
            "saved_at": datetime.now().isoformat(),
            "version": save_data.get("version", 1)
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/editor/projects/{project_id}/export")
async def export_editor_project(
    project_id: str,
    export_options: Dict[str, Any],
    current_user: models.User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Export editor project to video"""
    try:
        # TODO: Implement actual video export functionality
        # This would render the timeline into a final video

        # Mock export process
        export_id = f"export_{int(datetime.now().timestamp())}"

        return {
            "export_id": export_id,
            "status": "processing",
            "message": "Export started successfully",
            "estimated_time": "2-5 minutes",
            "export_options": export_options
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/editor/exports/{export_id}")
async def get_export_status(
    export_id: str,
    current_user: models.User = Depends(get_current_user)
):
    """Get export status"""
    try:
        # TODO: Implement actual export status tracking
        # For now, return mock status
        return {
            "export_id": export_id,
            "status": "completed",
            "progress": 100,
            "download_url": "https://example.com/exported_video.mp4",
            "file_size": "15.2 MB",
            "duration": "45.5s"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


# --- Timeline Operations ---

@app.post("/editor/projects/{project_id}/clips")
async def add_clip_to_timeline(
    project_id: str,
    clip_data: TimelineClip,
    current_user: models.User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Add a clip to the timeline"""
    try:
        # TODO: Implement actual clip addition to timeline
        return {
            "message": "Clip added to timeline",
            "clip": {
                "id": clip_data.id,
                "start": clip_data.start,
                "end": clip_data.end,
                "duration": clip_data.duration,
                "url": clip_data.url,
                "title": clip_data.title,
                "track": clip_data.track
            }
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.put("/editor/projects/{project_id}/clips/{clip_id}")
async def update_timeline_clip(
    project_id: str,
    clip_id: str,
    clip_updates: Dict[str, Any],
    current_user: models.User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Update a clip on the timeline"""
    try:
        # TODO: Implement actual clip update functionality
        return {
            "message": "Clip updated successfully",
            "clip_id": clip_id,
            "updates": clip_updates,
            "updated_at": datetime.now().isoformat()
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.delete("/editor/projects/{project_id}/clips/{clip_id}")
async def delete_timeline_clip(
    project_id: str,
    clip_id: str,
    current_user: models.User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Delete a clip from the timeline"""
    try:
        # TODO: Implement actual clip deletion
        return {
            "message": "Clip deleted from timeline",
            "clip_id": clip_id
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/editor/projects/{project_id}/clips/{clip_id}/split")
async def split_timeline_clip(
    project_id: str,
    clip_id: str,
    split_time: float,
    current_user: models.User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Split a clip at the specified time"""
    try:
        # TODO: Implement actual clip splitting
        new_clip_id = f"{clip_id}_split_{int(datetime.now().timestamp())}"

        return {
            "message": "Clip split successfully",
            "original_clip_id": clip_id,
            "new_clip_id": new_clip_id,
            "split_time": split_time
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/editor/projects/{project_id}/clips/{clip_id}/duplicate")
async def duplicate_timeline_clip(
    project_id: str,
    clip_id: str,
    current_user: models.User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Duplicate a clip on the timeline"""
    try:
        # TODO: Implement actual clip duplication
        new_clip_id = f"{clip_id}_dup_{int(datetime.now().timestamp())}"

        return {
            "message": "Clip duplicated successfully",
            "original_clip_id": clip_id,
            "new_clip_id": new_clip_id
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


# --- Text Overlays and Effects ---

@app.post("/editor/projects/{project_id}/text-overlays")
async def add_text_overlay(
    project_id: str,
    overlay_data: TextOverlay,
    current_user: models.User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Add a text overlay to the project"""
    try:
        # TODO: Implement actual text overlay addition
        return {
            "message": "Text overlay added",
            "overlay": {
                "id": overlay_data.id,
                "text": overlay_data.text,
                "x": overlay_data.x,
                "y": overlay_data.y,
                "fontSize": overlay_data.fontSize,
                "color": overlay_data.color,
                "startTime": overlay_data.startTime,
                "endTime": overlay_data.endTime
            }
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.put("/editor/projects/{project_id}/text-overlays/{overlay_id}")
async def update_text_overlay(
    project_id: str,
    overlay_id: str,
    overlay_updates: Dict[str, Any],
    current_user: models.User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Update a text overlay"""
    try:
        # TODO: Implement actual text overlay update
        return {
            "message": "Text overlay updated",
            "overlay_id": overlay_id,
            "updates": overlay_updates
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.delete("/editor/projects/{project_id}/text-overlays/{overlay_id}")
async def delete_text_overlay(
    project_id: str,
    overlay_id: str,
    current_user: models.User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Delete a text overlay"""
    try:
        # TODO: Implement actual text overlay deletion
        return {
            "message": "Text overlay deleted",
            "overlay_id": overlay_id
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/editor/projects/{project_id}/effects")
async def add_effect(
    project_id: str,
    effect_data: Dict[str, Any],
    current_user: models.User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Add an effect to the project"""
    try:
        # TODO: Implement actual effect addition
        effect_id = f"effect_{int(datetime.now().timestamp())}"

        return {
            "message": "Effect added",
            "effect": {
                "id": effect_id,
                "type": effect_data.get("type"),
                "parameters": effect_data.get("parameters", {}),
                "startTime": effect_data.get("startTime"),
                "endTime": effect_data.get("endTime"),
                "target": effect_data.get("target")  # clip_id or "global"
            }
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/editor/effects/templates")
async def get_effect_templates(
    current_user: models.User = Depends(get_current_user)
):
    """Get available effect templates"""
    try:
        # TODO: Implement actual effect templates from database
        return [
            {
                "id": "fade_in",
                "name": "Fade In",
                "description": "Gradually fade in the video",
                "category": "transition",
                "parameters": {
                    "duration": {"type": "number", "default": 1.0, "min": 0.1, "max": 5.0}
                }
            },
            {
                "id": "fade_out",
                "name": "Fade Out",
                "description": "Gradually fade out the video",
                "category": "transition",
                "parameters": {
                    "duration": {"type": "number", "default": 1.0, "min": 0.1, "max": 5.0}
                }
            },
            {
                "id": "blur",
                "name": "Blur",
                "description": "Apply blur effect",
                "category": "filter",
                "parameters": {
                    "intensity": {"type": "number", "default": 5, "min": 1, "max": 20}
                }
            },
            {
                "id": "brightness",
                "name": "Brightness",
                "description": "Adjust video brightness",
                "category": "color",
                "parameters": {
                    "value": {"type": "number", "default": 1.0, "min": 0.1, "max": 3.0}
                }
            }
        ]
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.delete("/videos/{video_id}")
async def delete_video(
    video_id: int,
    current_user: models.User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    video = db.query(models.Video).filter(
        models.Video.id == video_id,
        models.Video.user_id == current_user.id
    ).first()

    if not video:
        raise HTTPException(status_code=404, detail="Video not found")

    # Delete associated clips
    clips = db.query(models.VideoClip).filter(
        models.VideoClip.video_id == video.id).all()
    for clip in clips:
        db.delete(clip)

    # Delete video record
    db.delete(video)
    db.commit()

    return {"detail": "Video deleted successfully"}

# --- URL Processing Endpoints ---


class URLRequest(BaseModel):
    url: str


@app.post("/validate-url", response_model=URLValidationResponse)
async def validate_video_url(request: URLRequest):
    """Validate if URL is from a supported platform - Public endpoint"""
    try:
        processor = url_processor.URLVideoProcessor()
        validation = processor.validate_url(request.url)

        return URLValidationResponse(
            valid=validation['valid'],
            platform=validation.get('platform'),
            video_id=validation.get('video_id'),
            error=validation.get('error')
        )
    except Exception as e:
        logger.error(f"URL validation error: {str(e)}")
        return URLValidationResponse(
            valid=False,
            error=str(e)
        )


@app.post("/url-metadata")
async def get_url_metadata(request: URLRequest):
    """Get video metadata from URL without downloading - Public endpoint"""
    try:
        processor = url_processor.URLVideoProcessor()
        metadata = processor.get_video_metadata(request.url)

        if 'error' in metadata:
            raise HTTPException(status_code=400, detail=metadata['error'])

        return metadata
    except Exception as e:
        logger.error(f"Metadata extraction error: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# Optional authentication dependency


async def get_current_user_optional(token: str = Depends(oauth2_scheme), db: Session = Depends(get_db)):
    """Get current user if authenticated, otherwise return None"""
    if not token:
        return None
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        username: str = payload.get("sub")
        if username is None:
            return None
        user = get_user(db, username=username)
        return user
    except JWTError:
        return None


class URLProcessResponse(BaseModel):
    clips: List[ProcessedClipDetail]


# def group_words_into_segments(
#     word_segments: List[Dict[str, Any]],
#     min_duration: float,
#     max_duration: float
# ) -> List[Dict[str, Any]]:
#     """
#     Groups word-level segments into larger, coherent segments with intelligent duration optimization.
#     Uses max_duration as a maximum limit, creating shorter clips when content is more engaging.
#     """
#     if not word_segments:
#         return []

#     final_segments = []
#     current_segment_words = []
#     segment_start_time = word_segments[0]['start']

#     logger.info(f"Starting segment grouping with {len(word_segments)} words")


#     for i, word in enumerate(word_segments):
#         current_segment_words.append(word['text'])
#         current_duration = word['end'] - segment_start_time

#         # Look for natural break points
#         is_sentence_end = word['text'].rstrip().endswith(('.', '!', '?'))
#         is_long_pause = (i < len(word_segments) - 1 and
#                          word_segments[i + 1]['start'] - word['end'] > 1.0)

#         # Determine if we should end the segment
#         should_end_segment = False

#         if current_duration >= max_duration:
#             # Must end - we've hit the maximum
#             should_end_segment = True
#         elif current_duration >= min_duration:
#             # We can end if we find a good break point
#             if is_sentence_end or is_long_pause:
#                 should_end_segment = True
#             # For shorter clips (15-25s), prefer to end early if content is engaging
#             elif current_duration >= 15 and _is_engaging_content(" ".join(current_segment_words)):
#                 should_end_segment = True
#         elif i == len(word_segments) - 1:
#             # Last segment - include if it meets minimum duration
#             should_end_segment = current_duration >= min_duration

#         if should_end_segment:
#             segment_text = " ".join(current_segment_words)
#             final_segments.append({
#                 'text': segment_text,
#                 'start': segment_start_time,
#                 'end': word['end']
#             })

#             # Reset for next segment
#             current_segment_words = []
#             if i + 1 < len(word_segments):
#                 segment_start_time = word_segments[i + 1]['start']

#     return final_segments
def group_words_into_segments(
    word_segments: List[Dict[str, Any]],
    min_duration: float,
    max_duration: float
) -> List[Dict[str, Any]]:
    """
    CORRECTED: Groups word-level segments into larger, coherent segments.
    This version fixes the bug where the last segment of the video was dropped.
    """
    if not word_segments:
        return []

    final_segments = []
    current_segment_words = []
    # Initialize segment_start_time safely
    segment_start_time = word_segments[0]['start']

    logger.info(f"Starting segment grouping with {len(word_segments)} words")

    for i, word in enumerate(word_segments):
        current_segment_words.append(word['text'])
        current_duration = word['end'] - segment_start_time

        is_sentence_end = word['text'].rstrip().endswith(('.', '!', '?'))
        is_long_pause = (i + 1 < len(word_segments) and
                         word_segments[i + 1]['start'] - word['end'] > 1.0)

        # Determine if we should end the segment based on clear, non-overlapping rules
        should_end_segment = False
        if current_duration >= max_duration:
            should_end_segment = True
        elif current_duration >= min_duration and (is_sentence_end or is_long_pause or _is_engaging_content(" ".join(current_segment_words))):
            should_end_segment = True

        if should_end_segment:
            segment_text = " ".join(current_segment_words)
            final_segments.append({
                'text': segment_text,
                'start': segment_start_time,
                'end': word['end']
            })

            # Reset for the next segment
            current_segment_words = []
            if i + 1 < len(word_segments):
                segment_start_time = word_segments[i + 1]['start']
            # If it was the last word that completed the segment, current_segment_words will be empty, and the loop will end.

    # --- START OF THE FIX ---
    # After the loop, process any remaining words that form the final segment.
    # This is the critical part that was missing/buggy in the original logic.
    if current_segment_words:
        last_word_end_time = word_segments[-1]['end']
        final_segment_duration = last_word_end_time - segment_start_time

        # Add the final segment only if it meets the minimum duration requirement.
        if final_segment_duration >= min_duration:
            segment_text = " ".join(current_segment_words)
            final_segments.append({
                'text': segment_text,
                'start': segment_start_time,
                'end': last_word_end_time
            })
            logger.info(
                f"Added final leftover segment of {final_segment_duration:.2f}s.")
    # --- END OF THE FIX ---

    return final_segments


def _is_engaging_content(text: str) -> bool:
    """Identify engaging content that works well in shorter clips"""
    engaging_indicators = [
        # Emotional triggers
        '!', '?', 'wow', 'amazing', 'incredible', 'shocking', 'unbelievable',
        # Educational hooks
        'secret', 'trick', 'hack', 'tip', 'mistake', 'wrong', 'right',
        # Superlatives
        'best', 'worst', 'first', 'last', 'only', 'never', 'always',
        # Action words
        'watch', 'look', 'see', 'check', 'try', 'do', 'make',
        # Controversy/debate
        'controversial', 'debate', 'argue', 'disagree', 'opinion'
    ]

    text_lower = text.lower()

    # Count engaging indicators
    indicator_count = sum(
        1 for indicator in engaging_indicators if indicator in text_lower)

    # Also check for questions and exclamations
    question_count = text.count('?')
    exclamation_count = text.count('!')

    # Content is engaging if it has multiple indicators or strong emotional markers
    return (indicator_count >= 2 or question_count >= 1 or exclamation_count >= 2)


@app.post("/process-instant", response_model=InstantProcessResponse)
async def process_instant_video_from_url(
    request: InstantProcessURLRequest,
    background_tasks: BackgroundTasks,
    current_user: Optional[models.User] = Depends(get_current_user_optional),
    db: Session = Depends(get_db)
):
    """
    Handles the "one-click" video processing flow FROM A URL.
    1. Downloads video from the URL.
    2. Generates short clips.
    3. Adds mandatory subtitles and optional emojis.
    4. Uploads final clips and saves records.
    """
    # --- Step 1: Setup Paths and Download Video from URL ---
    start_time = time.time()
    url_processor_instance = url_processor.URLVideoProcessor(TEMP_DIR)
    output_dir = os.path.join(TEMP_DIR, f"instant_output_{int(start_time)}")
    os.makedirs(output_dir, exist_ok=True)

    if current_user and current_user.subscription == "free" and db.query(models.Video).filter(models.Video.user_id == current_user.id).count() >= 5:
        raise HTTPException(
            status_code=402, detail="Free subscription limit reached (5 videos)")

    paths_to_cleanup = [output_dir]

    try:
        logger.info(f"Downloading video from URL: {request.url}")
        # Download the video file and get its local path
        video_path, download_metadata = url_processor_instance.download_video(
            request.url)
        if not video_path or not os.path.exists(video_path):
            raise ValueError(
                f"Failed to download video from URL. Metadata: {download_metadata}")

        # Add downloaded file to cleanup list
        paths_to_cleanup.append(video_path)

        video_filename = os.path.basename(video_path)
        logger.info(f"Successfully downloaded video to {video_path}")

        # --- Step 2: Create Main Video Record in Database ---
        if current_user:
            db_video = models.Video(
                user_id=current_user.id,
                filename=video_filename,
                original_path=request.url,
                status="processing"
            )
            db.add(db_video)
            db.commit()
            db.refresh(db_video)

        # --- Step 3: Call the Main Processing Function ---
        process_options = request.options
        logger.info(
            f"Calling process_video_with_enhancements with options: {process_options.model_dump()}")

        enhancement_options = {
            'add_subtitles': True,
            'add_emojis': process_options.add_emojis,
            'create_short_form': True,
            'platforms': [process_options.platform],
            'subtitle_style': process_options.subtitle_style,
            'max_short_clips': process_options.max_clips,
            'use_ai_features': True
        }

        # Use the downloaded local file path for processing
        results = process_video_with_enhancements(
            video_path=video_path,
            output_dir=output_dir,
            openai_api_key=os.getenv("OPENAI_API_KEY"),
            options=enhancement_options
        )

        if results.get('error'):
            raise ValueError(f"Video processing failed: {results['error']}")

        # --- Step 4: Upload Results and Save to Database ---
        final_clip_details = []
        processed_clips_map = results.get('short_form_clips', {})

        for platform, clip_paths in processed_clips_map.items():
            for clip_path in clip_paths:
                if os.path.exists(clip_path):
                    url = storage.upload_to_cloudinary(clip_path)
                    with VideoFileClip(clip_path) as clip:
                        duration = clip.duration

                    text_for_clip = f"Clip from {video_filename}"

                    if current_user:
                        db_clip = models.VideoClip(
                            video_id=db_video.id, url=url, duration=duration, status="completed")
                        db.add(db_clip)
                        db.commit()

                    final_clip_details.append(ProcessedClipDetail(
                        url=url,
                        text=text_for_clip,
                        start_time=0,
                        end_time=duration,
                        platform=platform
                    ))

        # --- Step 5: Finalize and Cleanup ---
        db_video.status = "completed"
        db_video.processed_count = len(final_clip_details)
        db.commit()

        def cleanup_files():
            for path in paths_to_cleanup:
                try:
                    if os.path.isfile(path):
                        os.remove(path)
                    elif os.path.isdir(path):
                        shutil.rmtree(path)
                except Exception as e:
                    logger.error(f"Error during cleanup of {path}: {e}")

        background_tasks.add_task(cleanup_files)

        logger.info(
            f"Instant processing completed in {time.time() - start_time:.2f} seconds.")
        return InstantProcessResponse(
            success=True,
            message=f"Successfully processed and created {len(final_clip_details)} clips from URL.",
            clips=final_clip_details
        )

    except Exception as e:
        logger.error(f"Error in /process-instant endpoint: {e}", exc_info=True)
        # Cleanup logic here as well
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/process-url", response_model=URLProcessResponse)
async def process_video_url(
    request: URLProcessRequest,
    background_tasks: BackgroundTasks,
    current_user: Optional[models.User] = Depends(get_current_user_optional),
    db: Session = Depends(get_db)
):
    """Download and process video from URL - Works with or without authentication"""
    # Check subscription limits only if user is authenticated
    if current_user and current_user.subscription == "free" and db.query(models.Video).filter(models.Video.user_id == current_user.id).count() >= 5:
        raise HTTPException(
            status_code=402, detail="Free subscription limit reached (5 videos)")

    paths_to_cleanup = []
    try:
        logger.info(f"Request: {request}")
        print(f"Request: {request}")
        # Validate URL first
        processor = url_processor.URLVideoProcessor(TEMP_DIR)
        validation = processor.validate_url(request.url)

        if not validation['valid']:
            raise HTTPException(status_code=400, detail=validation['error'])
        request.analyze_virality = True

        # Download video
        logger.info(f"Processing URL: {request.url}")
        print(f"Processing URL: {request.url}")
        video_path, download_metadata = processor.download_video(
            request.url, request.quality)

        paths_to_cleanup.append(video_path)

        with VideoFileClip(video_path) as clip:
            video_duration = clip.duration
        logger.info(f"Actual video duration is: {video_duration:.2f} seconds.")

        processing_video_path = video_path
        if request.subscription == 'free':
            logger.info(
                "Payload indicates 'free' subscription. Applying watermark to the main downloaded video.")

            # Define a new path for the watermarked video.
            watermarked_path = os.path.join(
                TEMP_DIR, f"wm_main_{os.path.basename(video_path)}")

            # Apply the watermark filter.
            video_processing.add_watermark(
                video_path, watermarked_path, "SmartClips.io")

            # All subsequent processing will use the watermarked version.
            processing_video_path = watermarked_path
            paths_to_cleanup.append(watermarked_path)
            logger.info(f"Watermarked video created at: {watermarked_path}")

        # Create video record only if user is authenticated
        db_video = None
        if current_user:
            db_video = models.Video(
                user_id=current_user.id,
                filename=f"url_{download_metadata['platform']}_{download_metadata['video_id']}.mp4",
                original_path=video_path,
                duration=download_metadata.get('duration', 0),
                resolution=download_metadata.get('resolution', 'unknown'),
                status="processing"
            )
            db.add(db_video)
            db.commit()
            db.refresh(db_video)

        # Process the video using existing pipeline
        transcript, timestamps = video_processing.transcribe_video(
            processing_video_path, TEMP_DIR)

        word_level_segments = video_processing.segment_transcript(
            transcript,
            timestamps,
            min_duration=request.min_duration,
            max_duration=request.max_duration,
            refine_with_ai=current_user.subscription != "free" if current_user else False
        )
        print(f"Word-level segments: {word_level_segments}")
        logger.info(f"Word-level segments: {word_level_segments}")

        segments = group_words_into_segments(
            word_level_segments,
            min_duration=request.min_duration,
            max_duration=request.max_duration
        )

        print(f"Segments created: {segments}")
        logger.info(f"Segments created: {segments}")

        valid_segments = []
        for s in segments:
            # Only keep segments that start before the video is over
            if s['start'] < video_duration:
                # IMPORTANT: Trim the end time to not exceed the video's actual duration
                s['end'] = min(s['end'], video_duration)
                valid_segments.append(s)

        # Overwrite the old, potentially invalid segments with our clean list
        segments = valid_segments
        print(f"Validated segments: {segments}")

        logger.info(
            f"Validated {len(segments)} segments to be within video duration.")

        if request.max_clips:
            segments = segments[:request.max_clips]

        logger.info(f"Created {len(segments)} segments from transcript.")

    # --- Step 4: Analyze each segment's text CONCURRENTLY ---
        analysis_results = []
        if request.analyze_virality and segments:
            logger.info(
                f"Analyzing virality for {len(segments)} segments in parallel...")
            # Create a list of analysis tasks, one for each segment's text
            analysis_tasks = [analyze_virality_with_openai(
                s['text']) for s in segments]
            # Run all API calls at the same time and wait for them all to complete
            analysis_results = await asyncio.gather(*analysis_tasks)
        else:
            # If not analyzing, create a list of Nones to match the number of segments
            analysis_results = [None] * len(segments)

        # --- Step 5: Create local video clips ---
        clipped_videos_paths = video_processing.clip_video_from_text(
            processing_video_path, segments, TEMP_DIR
        )

        # --- Step 6: Loop through results, upload, and build final response object ---
        final_clips_details: List[ProcessedClipDetail] = []

        # Zip combines the corresponding items from each list into tuples
        for segment, clip_path, analysis_data in zip(segments, clipped_videos_paths, analysis_results):
            # Upload the physical clip to get its public URL
            url = storage.upload_to_cloudinary(clip_path)

            # Create a ViralityAnalysisResult object from the dictionary if it exists
            virality_obj = ViralityAnalysisResult(
                **analysis_data) if analysis_data else None

            # Combine all information for this one clip

            clip_duration = segment['end'] - segment['start']

            final_clips_details.append(
                ProcessedClipDetail(
                    url=url,
                    text=segment['text'],
                    start_time=0.0,
                    end_time=clip_duration,
                    virality_analysis=virality_obj,
                    platform=validation.get('platform', 'unknown')
                )
            )

        # --- Step 7: Cleanup (No change) ---

        def cleanup():
            try:
                processor.cleanup_downloaded_file(video_path)
                for clip_path in clipped_videos_paths:
                    if os.path.exists(clip_path):
                        os.remove(clip_path)
            except Exception as e:
                logger.error(f"Cleanup error: {str(e)}")
        background_tasks.add_task(cleanup)

        print(f"Final clips details: {final_clips_details}")
        logging.info(f"Final clips details: {final_clips_details}")

        # --- Step 8: Return the final, structured response ---
        return URLProcessResponse(clips=final_clips_details)

    except Exception as e:
        logger.error(f"URL processing error: {e}", exc_info=True)
        # Your DB error handling can go here
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/advanced-process", response_model=AdvancedProcessingResponse)
async def advanced_video_processing(
    request: str = Form(...),
    file: UploadFile | None = File(default=None),
    background_tasks: BackgroundTasks = BackgroundTasks(),
    current_user: Optional[models.User] = Depends(get_current_user_optional),
    db: Session = Depends(get_db)
):
    """
    Advanced video processing with subtitles, emojis, clipart, and short-form content creation
    Supports both file upload and URL processing
    """
    try:
        request_data = json.loads(request)
        request_model = AdvancedProcessingRequest(**request_data)

        # Check subscription limits for authenticated users
        if current_user and current_user.subscription == "free":
            video_count = db.query(models.Video).filter(
                models.Video.user_id == current_user.id).count()
            if video_count >= 5:
                raise HTTPException(
                    status_code=402, detail="Free subscription limit reached (5 videos)")

        # Determine input source
        video_path = None
        temp_video_path = None

        if file:
            # Handle file upload
            temp_video_path = os.path.join(
                TEMP_DIR, f"advanced_{file.filename}")
            with open(temp_video_path, "wb") as temp_video:
                shutil.copyfileobj(file.file, temp_video)
            video_path = temp_video_path

        elif request_model.video_url:
            # Handle URL processing
            processor = url_processor.URLVideoProcessor()
            validation = processor.validate_url(request_model.video_url)

            if not validation['valid']:
                raise HTTPException(
                    status_code=400, detail=f"Invalid URL: {validation.get('error', 'Unknown error')}")

            # Download video
            video_path, download_meta = processor.download_video(
                request_model.video_url, quality="best")
            if 'error' in download_meta:
                raise HTTPException(
                    status_code=400, detail=download_meta['error'])

            # video_path = download_result['local_path']
            temp_video_path = video_path
        else:
            raise HTTPException(
                status_code=400, detail="Either file upload or video_url must be provided")

        # Create output directory
        output_dir = os.path.join(
            TEMP_DIR, f"advanced_output_{datetime.now().strftime('%Y%m%d_%H%M%S')}")
        os.makedirs(output_dir, exist_ok=True)

        # Initialize advanced processor
        processor = AdvancedVideoProcessor(
            openai_api_key=os.getenv("OPENAI_API_KEY"),
            temp_dir=TEMP_DIR
        )

        # Process video with all enhancements
        processing_options = {
            'add_subtitles': request_model.options.add_subtitles,
            'add_emojis': request_model.options.add_emojis,
            'add_clipart': request_model.options.add_clipart,
            'create_short_form': request_model.options.create_short_form,
            'platforms': request_model.options.platforms,
            'subtitle_style': request_model.options.subtitle_style,
            'max_short_clips': request_model.options.max_short_clips,
            'max_duration_seconds': request_model.options.max_duration_seconds
        }

        results = processor.process_video_comprehensive(
            video_path, output_dir, processing_options)

        # Upload processed videos to Cloudinary
        uploaded_videos = {}
        uploaded_short_clips = {}

        # Upload main processed videos
        for video_type, video_path_result in results.get('processed_videos', {}).items():
            if os.path.exists(video_path_result):
                url = storage.upload_to_cloudinary(video_path_result)
                uploaded_videos[video_type] = url

        # Upload short-form clips
        for platform, clips in results.get('short_form_clips', {}).items():
            uploaded_clips = []
            for clip_path in clips:
                if os.path.exists(clip_path):
                    url = storage.upload_to_cloudinary(clip_path)
                    uploaded_clips.append(url)
            uploaded_short_clips[platform] = uploaded_clips

        # Save to database if user is authenticated
        if current_user:
            db_video = models.Video(
                user_id=current_user.id,
                filename=file.filename if file else "url_video",
                original_path=request_model.video_url,
                status="completed",
                processed_count=len(uploaded_videos) + sum(len(clips)
                                                           for clips in uploaded_short_clips.values())
            )
            db.add(db_video)
            db.commit()
            db.refresh(db_video)

            # Save clips to database
            for video_type, url in uploaded_videos.items():
                db_clip = models.VideoClip(
                    video_id=db_video.id,
                    url=url,
                    duration=0,  # Would need to calculate actual duration
                    status="completed"
                )
                db.add(db_clip)

            for platform, clips in uploaded_short_clips.items():
                for url in clips:
                    db_clip = models.VideoClip(
                        video_id=db_video.id,
                        url=url,
                        duration=0,  # Would need to calculate actual duration
                        status="completed"
                    )
                    db.add(db_clip)

            db.commit()

        # Schedule cleanup
        def cleanup():
            try:
                if temp_video_path and os.path.exists(temp_video_path):
                    os.remove(temp_video_path)
                if os.path.exists(output_dir):
                    shutil.rmtree(output_dir)
            except Exception as e:
                logger.error(f"Cleanup error: {str(e)}")

        background_tasks.add_task(cleanup)

        return AdvancedProcessingResponse(
            success=True,
            message="Video processed successfully with advanced features",
            original_video=video_path,
            processed_videos=uploaded_videos,
            short_form_clips=uploaded_short_clips,
            metadata=results.get('metadata', {}),
            processing_time=results.get('processing_time', 0.0)
        )

    except Exception as e:
        logger.error(f"Advanced processing error: {str(e)}")
        return AdvancedProcessingResponse(
            success=False,
            message="Processing failed",
            original_video="",
            error=str(e)
        )


class GenerationRequest(BaseModel):
    prompt: str
    image: str
    platform: str
    duration: int  # Duration of the video in seconds


class GenerationResponse(BaseModel):
    script: str


# Set your OpenAI API Key
openai_client = openai.OpenAI(api_key=os.getenv("OPENAI_API_KEY"))


os.environ['GOOGLE_APPLICATION_CREDENTIALS'] = 'angular-argon-452914-f1-17bffe088833.json'


@app.post("/generate", response_model=GenerationResponse)
async def generate_video_script(request: GenerationRequest):
    try:

        detailed_prompt = (
            f"Create a highly detailed video script for {request.platform} based on the following idea:\n\n"
            f"Idea: {request.prompt}\n"
            f"Target Duration: {request.duration} seconds.\n\n"
            "The script must be clearly structured, alternating between SCENE descriptions and NARRATOR speeches.\n"
            "Each SCENE should be labeled like 'SCENE 1:', 'SCENE 2:', etc., and be extremely vivid and visual and look like this {request.image}.\n"
            "Each NARRATOR line should be emotional, storytelling-style, and connected to the scene.\n\n"
            "FORMAT STRICTLY LIKE THIS:\n\n"
            "SCENE 1:\n"
            "[Detailed description of the first visual scene — colors, environment, emotions, action.]\n\n"
            "NARRATOR:\n"
            "[Narration for the first scene — short, impactful, and expressive.]\n\n"
            "SCENE 2:\n"
            "[Detailed description of the second visual scene.]\n\n"
            "NARRATOR:\n"
            "[Narration for the second scene.]\n\n"
            "Create at least 3 to 5 SCENE and NARRATOR pairs."
        )

        # New way of calling Chat Completions
        response = openai_client.chat.completions.create(
            model="gpt-4",
            messages=[
                {"role": "system", "content": "You are a creative video scriptwriter."},
                {"role": "user", "content": detailed_prompt}
            ],
            temperature=0.7,
            max_tokens=800
        )

        script = response.choices[0].message.content

        return GenerationResponse(script=script)

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


# Define the request model for generating audio
class AudioGenerationRequest(BaseModel):
    script_text: str
    # Default to English if not provided
    language_code: Optional[str] = "en-US"
    voice_name: Optional[str] = "en-US-Wavenet-D"  # Default voice option

# Define the response model


class AudioGenerationResponse(BaseModel):
    audio_url: str  # The URL or file path of the generated audio


# Request model for audio and image
class AudioRequest(BaseModel):
    script_text: str
    session_name: str
    scene_number: int
    voice_type: str
    platform: str


class ImageRequest(BaseModel):
    script: dict
    session_name: str
    scene_number: int
    mediaType: str
    platform: str


# Helper function to create the session folder and subfolder structure
def create_session_folder(session_name):
    base_folder = f"static/sessions/{session_name}"

    # Check if the base folder exists, if not create it
    if not os.path.exists(base_folder):
        os.makedirs(base_folder, exist_ok=True)

    # Subfolders for images and audio inside the session folder
    image_folder = os.path.join(base_folder, "images")
    audio_folder = os.path.join(base_folder, "audio")

    # Create subfolders if they don't exist
    os.makedirs(image_folder, exist_ok=True)
    os.makedirs(audio_folder, exist_ok=True)

    return base_folder, image_folder, audio_folder


# ElevenLabs config
ELEVENLABS_API_KEY = os.getenv("ELEVENLABS_API_KEY")  # set this securely
VOICE_ID = "EXAVITQu4vr4xnSDxMaL"  # Example voice ID (you can change this)


def create_session_folder(session_name):
    base_folder = f"static/sessions/{session_name}"
    image_folder = os.path.join(base_folder, "images")
    audio_folder = os.path.join(base_folder, "audio")

    os.makedirs(image_folder, exist_ok=True)
    os.makedirs(audio_folder, exist_ok=True)

    return base_folder, image_folder, audio_folder


@app.post("/generate-audio")
async def generate_audio(request: AudioRequest):
    try:
        session_name = request.session_name
        scene_number = request.scene_number
        script_text = request.script_text
        voice_type = request.voice_type

        print(voice_type)

        # Create folders
        base_folder, image_folder, audio_folder = create_session_folder(
            session_name)

        # ElevenLabs API call
        url = f"https://api.elevenlabs.io/v1/text-to-speech/{voice_type}"

        headers = {
            "xi-api-key": ELEVENLABS_API_KEY,
            "Content-Type": "application/json"
        }

        payload = {
            "text": script_text,
            "model_id": "eleven_monolingual_v1",
            "voice_settings": {
                "stability": 0.5,
                "similarity_boost": 0.75
            }
        }

        response = requests.post(url, headers=headers, json=payload)

        if response.status_code != 200:
            raise HTTPException(
                status_code=500, detail=f"ElevenLabs error: {response.text}")

        # Save audio file
        audio_filename = os.path.join(
            audio_folder, f"audio_{scene_number}.mp3")
        with open(audio_filename, "wb") as f:
            f.write(response.content)

        print(f"Audio content written to: {audio_filename}")
        return {"audio_path": audio_filename}

    except Exception as e:
        print(f"Error: {e}")
        raise HTTPException(
            status_code=500, detail=f"Error generating audio: {str(e)}")

# Audio request


@app.post("/generate-google-audio")
async def generate_audio(request: AudioRequest):
    try:
        session_name = request.session_name
        scene_number = request.scene_number
        script_text = request.script_text

        # Create session folder and subfolders
        base_folder, image_folder, audio_folder = create_session_folder(
            session_name)

        # Google Text to Speech setup
        client = get_tts_client()
        if not client:
            raise HTTPException(
                status_code=500, detail="Google Cloud TTS not available")

        synthesis_input = texttospeech.SynthesisInput(text=script_text)
        voice = texttospeech.VoiceSelectionParams(
            language_code="en-US",
            ssml_gender=texttospeech.SsmlVoiceGender.NEUTRAL
        )
        audio_config = texttospeech.AudioConfig(
            audio_encoding=texttospeech.AudioEncoding.MP3
        )

        # Perform speech synthesis
        response = client.synthesize_speech(
            input=synthesis_input,
            voice=voice,
            audio_config=audio_config
        )

        # Ensure we get binary data and write it to a file
        audio_filename = os.path.join(
            audio_folder, f"audio_{scene_number}.mp3")

        # Write the binary audio content to a file
        with open(audio_filename, "wb") as out:
            # Ensure response.audio_content is a binary string
            out.write(response.audio_content)

        print(f"Audio content written to: {audio_filename}")

        # Return the path to the audio file
        return {"audio_path": audio_filename}

    except Exception as e:
        print(f"Error: {e}")
        raise HTTPException(
            status_code=500, detail=f"Error generating audio: {str(e)}")


@app.post("/generate-image")
async def generate_images(request: ImageRequest):
    try:
        session_name = request.session_name
        scene_number = request.scene_number
        prompt = request.script["script"]

        # Reuse the same session folder and subfolders for images
        base_folder, image_folder, audio_folder = create_session_folder(
            session_name)

        openai_api_key = os.getenv("OPENAI_API_KEY")
        if not openai_api_key:
            raise HTTPException(
                status_code=500, detail="Missing OpenAI API key")

        response = requests.post(
            "https://api.openai.com/v1/images/generations",
            headers={"Authorization": f"Bearer {openai_api_key}"},
            json={"prompt": prompt, "n": 1, "size": "1024x1024"}
        )

        if response.status_code != 200:
            raise HTTPException(
                status_code=500, detail=f"Failed to generate images: {response.text}")

        data = response.json()
        image_url = data["data"][0]["url"]

        # Save image in the images subfolder with a unique name per scene
        image_filename = os.path.join(
            image_folder, f"image_{scene_number}.jpg")
        img_data = requests.get(image_url).content
        with open(image_filename, "wb") as img_file:
            img_file.write(img_data)

        return {"image_path": image_filename}

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


def create_video_from_audio_and_image(audio_file, image_file, output_video, platform):
    try:
        # Define platform resolutions
        platform_resolutions = {
            "youtube": "1920x1080",  # YouTube 16:9
            "tiktok": "1080x1920",  # TikTok portrait
            "instagram": "1024x1024",  # Instagram square
            "linkedin": "1200x627",  # LinkedIn
            "twitter": "1200x675",  # Twitter
        }

        # Default resolution is 1024x1024
        resolution = platform_resolutions.get(platform.lower(), "1024x1024")

        # Get width and height from the resolution
        width, height = map(int, resolution.split('x'))

        # FFmpeg command to create a video with scaling and cropping
        cmd = [
            "ffmpeg",
            "-loop", "1",  # Loop image to match audio duration
            "-framerate", "1",  # 1 frame per second
            "-t", str(get_audio_duration(audio_file)),  # Duration of audio
            "-i", image_file,  # Input image file
            "-i", audio_file,
            # Scale and crop image
            "-vf", f"scale={width}x{height}:force_original_aspect_ratio=increase,crop={width}:{height}",
            "-c:v", "libx264",
            "-preset", "fast",
            "-c:a", "aac",  # Audio codec
            "-strict", "experimental",  # Experimental codecs
            "-shortest",  # Ensure video duration matches audio
            output_video  # Output video file
        ]

        # Run FFmpeg command
        subprocess.run(cmd, check=True)
        print(f"Video created: {output_video}")

    except subprocess.CalledProcessError as e:
        print(f"Error creating video: {e}")
        raise


def get_audio_duration(audio_file):
    """
    Gets the duration of the audio file using FFmpeg.
    """
    cmd = [
        "ffmpeg",
        "-i", audio_file,  # Input audio file
        "-f", "null",  # Discard output
        "-"
    ]
    result = subprocess.run(cmd, stderr=subprocess.PIPE, text=True)
    duration_line = next(
        line for line in result.stderr.splitlines() if "Duration" in line)
    duration_str = duration_line.split("Duration:")[1].split(",")[0].strip()
    h, m, s = map(float, duration_str.split(":"))
    return h * 3600 + m * 60 + s


class VideoRequest(BaseModel):
    session_name: str  # The session name for the video generation
    audio_files: List[str]  # List of audio file paths
    image_files: List[str]  # List of image file paths
    platform: str


# --- Facial AI Models ---

class FaceDetectionConfig(BaseModel):
    """Enhanced configuration for face detection and zoom effects"""
    zoom_level: float = 1.5
    transition_speed: float = 0.5
    detection_sensitivity: float = 0.7
    voice_threshold: float = 0.3
    padding: int = 50
    # Enhanced framing options
    include_torso: bool = True
    headroom_ratio: float = 0.15
    torso_ratio: float = 0.6
    # Dynamic switching options
    speaker_switch_threshold: float = 0.5
    transition_smoothness: float = 0.3
    speaker_memory_duration: float = 2.0

class FaceDetectionAnalyzeRequest(BaseModel):
    """Request model for face detection analysis"""
    video_url: Optional[str] = None
    config: Optional[FaceDetectionConfig] = FaceDetectionConfig()

class FaceDetectionProcessRequest(BaseModel):
    """Request model for face detection processing"""
    video_url: Optional[str] = None
    output_format: str = "mp4"
    config: Optional[FaceDetectionConfig] = FaceDetectionConfig()

class FaceDetectionPreviewRequest(BaseModel):
    """Request model for face detection preview"""
    video_url: Optional[str] = None
    config: Optional[FaceDetectionConfig] = FaceDetectionConfig()

class FaceDetectionResponse(BaseModel):
    """Response model for face detection operations"""
    success: bool
    message: str
    job_id: Optional[str] = None
    result_url: Optional[str] = None
    analysis_data: Optional[Dict[str, Any]] = None
    preview_data: Optional[Dict[str, Any]] = None
    error: Optional[str] = None

class FaceDetectionStatusResponse(BaseModel):
    """Response model for face detection job status"""
    job_id: str
    status: str  # pending, processing, completed, failed
    progress: float  # 0.0 to 1.0
    result_url: Optional[str] = None
    error: Optional[str] = None
    created_at: str
    completed_at: Optional[str] = None


@app.post("/generate-video")
async def generate_video(request: VideoRequest):
    try:
        session_name = request.session_name
        audio_files = request.audio_files
        image_files = request.image_files
        platform = request.platform

        if len(audio_files) != len(image_files):
            raise HTTPException(
                status_code=400, detail="Audio files and image files must have the same length.")

        video_files = []
        for i in range(len(audio_files)):
            audio_file = audio_files[i]
            image_file = image_files[i]
            output_video = os.path.join(
                f"static/sessions/{session_name}", f"scene_{i+1}.mp4")

            # Create video from audio and image with platform aspect ratio
            create_video_from_audio_and_image(
                audio_file, image_file, output_video, platform)
            video_files.append(output_video)

        final_video = os.path.join(
            f"static/sessions/{session_name}", "final_video.mp4")
        concatenate_videos(video_files, final_video)

        return {"final_video_path": final_video}

    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Error generating video: {str(e)}")


def concatenate_videos(video_files, final_output):
    """
    Concatenates multiple video files into one final video.
    """
    try:
        # Create a text file with all video file paths
        with open("video_list.txt", "w") as f:
            for video_file in video_files:
                f.write(f"file '{video_file}'\n")

        # FFmpeg command to concatenate videos
        cmd = ["ffmpeg", "-f", "concat", "-safe", "0", "-i",
               "video_list.txt", "-c", "copy", final_output]
        subprocess.run(cmd, check=True)

        print(f"Final video created: {final_output}")

        # Clean up the temporary video list file
        os.remove("video_list.txt")

    except subprocess.CalledProcessError as e:
        print(f"Error concatenating videos: {e}")
        raise


@app.get("/get-el-voices")
def get_el_voices():
    try:
        url = "https://api.elevenlabs.io/v1/voices"
        headers = {
            "xi-api-key": ELEVENLABS_API_KEY
        }

        response = requests.get(url, headers=headers)

        if response.status_code == 200:
            voices = response.json().get("voices", [])
            return {"voices": voices}
        else:
            raise HTTPException(
                status_code=response.status_code, detail=response.text)

    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Error retrieving voices: {str(e)}")





@app.post("/compress-and-upload/")
async def compress_and_upload(file: UploadFile = File(...)):
    """
    Receives a video file, compresses it using FFmpeg, uploads the
    compressed version to Cloudinary, and returns the secure URL.
    This is a standalone utility endpoint.
    """
    # Use the TEMP_DIR you have already defined
    temp_dir = TEMP_DIR

    # Generate unique filenames to handle concurrent requests safely
    unique_id = uuid.uuid4()
    temp_input_path = os.path.join(
        temp_dir, f"{unique_id}_input_{file.filename}")
    temp_output_path = os.path.join(temp_dir, f"{unique_id}_compressed.mp4")

    try:
        # 1. Save the originally uploaded video to a temporary file
        with open(temp_input_path, "wb") as buffer:
            shutil.copyfileobj(file.file, buffer)

        # 2. Compress the video using ffmpeg-python. Your existing FFmpeg
        logger.info(f"Starting compression for {file.filename}...")
        logger.info(f"Starting robust compression for {file.filename}...")
        try:
            (
                ffmpeg
                .input(temp_input_path)
                .output(
                    temp_output_path,
                    vcodec='libx264',
                    # Use a slightly higher quality (lower CRF is better). 23 is a great balance.
                    crf=23,
                    # Slower preset gives better quality and compatibility. 'fast' is also a good option if 'medium' is too slow.
                    preset='medium',
                    acodec='aac',
                    audio_bitrate='128k',
                    # Force a Constant Frame Rate (CFR) of 30 fps. This is VITAL for moviepy.
                    vf='fps=30',
                    # Set a universally compatible pixel format.
                    pix_fmt='yuv420p',
                    # Optimizes the video for web streaming.
                    movflags='+faststart'
                )
                .run(quiet=True, overwrite_output=True)
            )
            logger.info("Robust compression finished successfully.")
        except ffmpeg.Error as e:
            # Catch and log specific FFmpeg errors for easier debugging
            logger.error(f"FFmpeg Error: {e.stderr.decode()}")
            raise HTTPException(
                status_code=500, detail=f"Video compression failed.")

        # 3. Upload the *compressed* video to Cloudinary using your existing config
        logger.info(f"Uploading compressed file to Cloudinary...")
        upload_result = cloudinary.uploader.upload(
            temp_output_path,
            resource_type="video",
        )
        logger.info("Upload to Cloudinary complete.")

        secure_url = upload_result.get('secure_url')
        if not secure_url:
            raise HTTPException(
                status_code=500, detail="Cloudinary upload failed, no URL returned.")

        # 4. Return the final URL to the frontend
        return {"url": secure_url}

    except Exception as e:
        # Catch any other errors during the process
        raise HTTPException(status_code=500, detail=str(e))

    finally:
        # 5. CRITICAL: Clean up all temporary files regardless of success or failure
        logger.info("Cleaning up temporary compression files...")
        if os.path.exists(temp_input_path):
            os.remove(temp_input_path)
        if os.path.exists(temp_output_path):
            os.remove(temp_output_path)


# --- Facial AI Processing Endpoints ---

# Global facial AI processor instance
facial_ai_processor = FacialAIProcessor(
    faces_api_key="eyJraWQiOm51bGwsImFsZyI6IlJTMjU2In0"
)

# In-memory job storage (in production, use Redis or database)
face_detection_jobs = {}

@app.post("/api/face-detection/analyze", response_model=FaceDetectionResponse)
async def analyze_video_for_faces(
    request: FaceDetectionAnalyzeRequest = None,
    file: UploadFile = File(None),
    current_user: models.User = Depends(get_current_user)
):
    """
    Analyze video for faces and speaking segments
    """
    try:
        logger.info("Starting face detection analysis")

        # Handle file upload or URL
        video_path = None
        temp_file = None

        if file:
            # Handle uploaded file
            temp_file = tempfile.mktemp(suffix=".mp4")
            with open(temp_file, "wb") as buffer:
                shutil.copyfileobj(file.file, buffer)
            video_path = temp_file
        elif request and request.video_url:
            # Handle URL - download video first
            video_path = await url_processor.download_video(request.video_url)
        else:
            raise HTTPException(status_code=400, detail="Either file upload or video_url is required")

        if not video_path or not os.path.exists(video_path):
            raise HTTPException(status_code=400, detail="Invalid video file or URL")

        # Create enhanced zoom config
        config = ZoomConfig()
        if request and request.config:
            config.zoom_level = request.config.zoom_level
            config.transition_speed = request.config.transition_speed
            config.detection_sensitivity = request.config.detection_sensitivity
            config.voice_threshold = request.config.voice_threshold
            config.padding = request.config.padding
            # Enhanced parameters
            config.include_torso = request.config.include_torso
            config.headroom_ratio = request.config.headroom_ratio
            config.torso_ratio = request.config.torso_ratio
            config.speaker_switch_threshold = request.config.speaker_switch_threshold
            config.transition_smoothness = request.config.transition_smoothness
            config.speaker_memory_duration = request.config.speaker_memory_duration

        # Analyze video
        analysis_result = await facial_ai_processor.analyze_video(video_path, config)

        # Clean up temporary file
        if temp_file and os.path.exists(temp_file):
            os.remove(temp_file)

        return FaceDetectionResponse(
            success=True,
            message="Video analysis completed successfully",
            analysis_data=analysis_result
        )

    except Exception as e:
        logger.error(f"Error in face detection analysis: {str(e)}")
        # Clean up on error
        if temp_file and os.path.exists(temp_file):
            os.remove(temp_file)

        return FaceDetectionResponse(
            success=False,
            message="Face detection analysis failed",
            error=str(e)
        )

@app.post("/api/face-detection/process", response_model=FaceDetectionResponse)
async def process_video_with_face_detection(
    background_tasks: BackgroundTasks,
    request: FaceDetectionProcessRequest = None,
    file: UploadFile = File(None),
    current_user: models.User = Depends(get_current_user)
):
    """
    Process video with dynamic face zoom effects
    """
    try:
        logger.info("Starting face detection video processing")

        # Generate job ID
        job_id = str(uuid.uuid4())

        # Initialize job status
        face_detection_jobs[job_id] = {
            "status": "pending",
            "progress": 0.0,
            "created_at": datetime.now().isoformat(),
            "user_id": current_user.id
        }

        # Handle file upload or URL
        video_path = None
        temp_file = None

        if file:
            # Handle uploaded file
            temp_file = tempfile.mktemp(suffix=".mp4")
            with open(temp_file, "wb") as buffer:
                shutil.copyfileobj(file.file, buffer)
            video_path = temp_file
        elif request and request.video_url:
            # Handle URL - download video first
            video_path = await url_processor.download_video(request.video_url)
        else:
            raise HTTPException(status_code=400, detail="Either file upload or video_url is required")

        if not video_path or not os.path.exists(video_path):
            raise HTTPException(status_code=400, detail="Invalid video file or URL")

        # Create enhanced zoom config
        config = ZoomConfig()
        if request and request.config:
            config.zoom_level = request.config.zoom_level
            config.transition_speed = request.config.transition_speed
            config.detection_sensitivity = request.config.detection_sensitivity
            config.voice_threshold = request.config.voice_threshold
            config.padding = request.config.padding
            # Enhanced parameters
            config.include_torso = request.config.include_torso
            config.headroom_ratio = request.config.headroom_ratio
            config.torso_ratio = request.config.torso_ratio
            config.speaker_switch_threshold = request.config.speaker_switch_threshold
            config.transition_smoothness = request.config.transition_smoothness
            config.speaker_memory_duration = request.config.speaker_memory_duration

        # Start background processing
        background_tasks.add_task(
            process_face_detection_background,
            job_id,
            video_path,
            config,
            current_user.id,
            temp_file
        )

        return FaceDetectionResponse(
            success=True,
            message="Face detection processing started",
            job_id=job_id
        )

    except Exception as e:
        logger.error(f"Error starting face detection processing: {str(e)}")
        return FaceDetectionResponse(
            success=False,
            message="Failed to start face detection processing",
            error=str(e)
        )

@app.get("/api/face-detection/status/{job_id}", response_model=FaceDetectionStatusResponse)
async def get_face_detection_status(
    job_id: str,
    current_user: models.User = Depends(get_current_user)
):
    """
    Check processing status of face detection job
    """
    if job_id not in face_detection_jobs:
        raise HTTPException(status_code=404, detail="Job not found")

    job = face_detection_jobs[job_id]

    # Check if user owns this job
    if job.get("user_id") != current_user.id:
        raise HTTPException(status_code=403, detail="Access denied")

    return FaceDetectionStatusResponse(
        job_id=job_id,
        status=job["status"],
        progress=job["progress"],
        result_url=job.get("result_url"),
        error=job.get("error"),
        created_at=job["created_at"],
        completed_at=job.get("completed_at")
    )

@app.post("/api/face-detection/preview", response_model=FaceDetectionResponse)
async def generate_face_detection_preview(
    request: FaceDetectionPreviewRequest = None,
    file: UploadFile = File(None),
    current_user: models.User = Depends(get_current_user)
):
    """
    Generate preview with face tracking overlay
    """
    try:
        logger.info("Generating face detection preview")

        # Handle file upload or URL
        video_path = None
        temp_file = None

        if file:
            # Handle uploaded file
            temp_file = tempfile.mktemp(suffix=".mp4")
            with open(temp_file, "wb") as buffer:
                shutil.copyfileobj(file.file, buffer)
            video_path = temp_file
        elif request and request.video_url:
            # Handle URL - download video first
            video_path = await url_processor.download_video(request.video_url)
        else:
            raise HTTPException(status_code=400, detail="Either file upload or video_url is required")

        if not video_path or not os.path.exists(video_path):
            raise HTTPException(status_code=400, detail="Invalid video file or URL")

        # Create enhanced zoom config
        config = ZoomConfig()
        if request and request.config:
            config.zoom_level = request.config.zoom_level
            config.transition_speed = request.config.transition_speed
            config.detection_sensitivity = request.config.detection_sensitivity
            config.voice_threshold = request.config.voice_threshold
            config.padding = request.config.padding
            # Enhanced parameters
            config.include_torso = request.config.include_torso
            config.headroom_ratio = request.config.headroom_ratio
            config.torso_ratio = request.config.torso_ratio
            config.speaker_switch_threshold = request.config.speaker_switch_threshold
            config.transition_smoothness = request.config.transition_smoothness
            config.speaker_memory_duration = request.config.speaker_memory_duration

        # Generate preview
        preview_data = await facial_ai_processor.generate_preview(video_path, config)

        # Clean up temporary file
        if temp_file and os.path.exists(temp_file):
            os.remove(temp_file)

        return FaceDetectionResponse(
            success=True,
            message="Preview generated successfully",
            preview_data=preview_data
        )

    except Exception as e:
        logger.error(f"Error generating face detection preview: {str(e)}")
        # Clean up on error
        if temp_file and os.path.exists(temp_file):
            os.remove(temp_file)

        return FaceDetectionResponse(
            success=False,
            message="Preview generation failed",
            error=str(e)
        )

async def process_face_detection_background(
    job_id: str,
    video_path: str,
    config: ZoomConfig,
    user_id: int,
    temp_file: str = None
):
    """
    Background task for processing video with face detection
    """
    try:
        # Update job status
        face_detection_jobs[job_id]["status"] = "processing"
        face_detection_jobs[job_id]["progress"] = 0.1

        # Generate output path
        output_filename = f"face_enhanced_{job_id}.mp4"
        output_path = os.path.join("backend/temp", output_filename)

        # Ensure temp directory exists
        os.makedirs("backend/temp", exist_ok=True)

        # Update progress
        face_detection_jobs[job_id]["progress"] = 0.3

        # Process video with face detection
        processed_path = await facial_ai_processor.process_video_with_face_zoom(
            video_path, output_path, config
        )

        # Update progress
        face_detection_jobs[job_id]["progress"] = 0.8

        # Upload to Cloudinary
        upload_result = cloudinary.uploader.upload(
            processed_path,
            resource_type="video",
            folder="smartclips/face_enhanced",
            public_id=f"face_enhanced_{job_id}"
        )

        result_url = upload_result["secure_url"]

        # Update job completion
        face_detection_jobs[job_id].update({
            "status": "completed",
            "progress": 1.0,
            "result_url": result_url,
            "completed_at": datetime.now().isoformat()
        })

        # Clean up temporary files
        if temp_file and os.path.exists(temp_file):
            os.remove(temp_file)
        if os.path.exists(processed_path):
            os.remove(processed_path)

        logger.info(f"Face detection processing completed for job {job_id}")

    except Exception as e:
        logger.error(f"Error in background face detection processing: {str(e)}")

        # Update job with error
        face_detection_jobs[job_id].update({
            "status": "failed",
            "error": str(e),
            "completed_at": datetime.now().isoformat()
        })

        # Clean up on error
        if temp_file and os.path.exists(temp_file):
            os.remove(temp_file)


if __name__ == "__main__":

    uvicorn.run(app, host="0.0.0.0", port=8000)
