"""
Simple test script for Facial AI functionality
Tests the core components without requiring full backend dependencies
"""

import sys
import os
import tempfile
import numpy as np
import cv2
from facial_ai_processor import FacialAIProcessor, ZoomConfig, FaceDetection, VoiceActivity

def create_test_video():
    """Create a simple test video for testing"""
    temp_file = tempfile.mktemp(suffix=".mp4")
    
    # Create a simple 3-second video with a moving rectangle (simulating a face)
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    out = cv2.VideoWriter(temp_file, fourcc, 30.0, (640, 480))
    
    for frame_num in range(90):  # 3 seconds at 30 fps
        # Create a black frame
        frame = np.zeros((480, 640, 3), dtype=np.uint8)
        
        # Add a moving white rectangle (simulating a face)
        x = 200 + int(50 * np.sin(frame_num * 0.1))
        y = 200 + int(30 * np.cos(frame_num * 0.1))
        cv2.rectangle(frame, (x, y), (x + 100, y + 120), (255, 255, 255), -1)
        
        out.write(frame)
    
    out.release()
    return temp_file

def test_processor_initialization():
    """Test that the processor initializes correctly"""
    print("Testing processor initialization...")
    
    try:
        processor = FacialAIProcessor(faces_api_key="test_key")
        assert processor.faces_api_key == "test_key"
        assert processor.face_detection is not None
        assert processor.face_mesh is not None
        print("✓ Processor initialization successful")
        return True
    except Exception as e:
        print(f"✗ Processor initialization failed: {e}")
        return False

def test_zoom_config():
    """Test ZoomConfig functionality"""
    print("Testing ZoomConfig...")
    
    try:
        config = ZoomConfig()
        assert config.zoom_level == 1.5
        assert config.transition_speed == 0.5
        assert config.detection_sensitivity == 0.7
        assert config.voice_threshold == 0.3
        assert config.padding == 50
        
        # Test custom config
        custom_config = ZoomConfig(
            zoom_level=2.0,
            transition_speed=1.0,
            detection_sensitivity=0.8,
            voice_threshold=0.4,
            padding=75
        )
        assert custom_config.zoom_level == 2.0
        assert custom_config.transition_speed == 1.0
        
        print("✓ ZoomConfig tests passed")
        return True
    except Exception as e:
        print(f"✗ ZoomConfig tests failed: {e}")
        return False

def test_data_classes():
    """Test data classes"""
    print("Testing data classes...")
    
    try:
        # Test FaceDetection
        face = FaceDetection(
            timestamp=1.5,
            bbox=(100, 100, 50, 60),
            confidence=0.85,
            landmarks=[(110, 110), (120, 115)],
            speaker_id=1
        )
        assert face.timestamp == 1.5
        assert face.bbox == (100, 100, 50, 60)
        assert face.confidence == 0.85
        
        # Test VoiceActivity
        voice = VoiceActivity(
            start_time=2.0,
            end_time=5.0,
            confidence=0.9,
            speaker_id=0
        )
        assert voice.start_time == 2.0
        assert voice.end_time == 5.0
        assert voice.confidence == 0.9
        
        print("✓ Data classes tests passed")
        return True
    except Exception as e:
        print(f"✗ Data classes tests failed: {e}")
        return False

def test_face_detection_basic():
    """Test basic face detection functionality"""
    print("Testing basic face detection...")
    
    try:
        processor = FacialAIProcessor()
        
        # Create a simple test image with a face-like rectangle
        test_image = np.zeros((480, 640, 3), dtype=np.uint8)
        # Add a white rectangle that might be detected as a face
        cv2.rectangle(test_image, (200, 200), (300, 320), (255, 255, 255), -1)
        
        # Convert to RGB for MediaPipe
        rgb_image = cv2.cvtColor(test_image, cv2.COLOR_BGR2RGB)
        
        # Try to process the image
        results = processor.face_detection.process(rgb_image)
        
        print(f"✓ Face detection processing completed (detections: {len(results.detections) if results.detections else 0})")
        return True
    except Exception as e:
        print(f"✗ Face detection test failed: {e}")
        return False

def test_utility_functions():
    """Test utility functions"""
    print("Testing utility functions...")
    
    try:
        processor = FacialAIProcessor()
        
        # Test merge voice segments
        segments = [
            VoiceActivity(start_time=1.0, end_time=3.0, confidence=0.8),
            VoiceActivity(start_time=2.5, end_time=4.0, confidence=0.9),  # Overlapping
            VoiceActivity(start_time=6.0, end_time=8.0, confidence=0.7),  # Separate
        ]
        
        merged = processor._merge_voice_segments(segments)
        assert len(merged) == 2  # Should merge overlapping segments
        assert merged[0].start_time == 1.0
        assert merged[0].end_time == 4.0
        
        # Test calculate average bbox
        faces = [
            FaceDetection(timestamp=1.0, bbox=(100, 100, 50, 60), confidence=0.8),
            FaceDetection(timestamp=1.5, bbox=(110, 105, 50, 60), confidence=0.85),
            FaceDetection(timestamp=2.0, bbox=(105, 110, 50, 60), confidence=0.9),
        ]
        
        avg_bbox = processor._calculate_average_bbox(faces)
        assert avg_bbox == (105, 105, 50, 60)
        
        print("✓ Utility functions tests passed")
        return True
    except Exception as e:
        print(f"✗ Utility functions tests failed: {e}")
        return False

def test_video_duration():
    """Test video duration calculation"""
    print("Testing video duration calculation...")
    
    try:
        processor = FacialAIProcessor()
        
        # Create a test video
        video_path = create_test_video()
        
        try:
            duration = processor._get_video_duration(video_path)
            print(f"✓ Video duration calculation successful: {duration}s")
            
            # Clean up
            if os.path.exists(video_path):
                os.remove(video_path)
            
            return True
        except Exception as e:
            # Clean up on error
            if os.path.exists(video_path):
                os.remove(video_path)
            raise e
            
    except Exception as e:
        print(f"✗ Video duration test failed: {e}")
        return False

def run_all_tests():
    """Run all tests and report results"""
    print("=" * 50)
    print("FACIAL AI SYSTEM TESTS")
    print("=" * 50)
    
    tests = [
        test_processor_initialization,
        test_zoom_config,
        test_data_classes,
        test_face_detection_basic,
        test_utility_functions,
        test_video_duration,
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"✗ Test {test.__name__} crashed: {e}")
            failed += 1
        print()
    
    print("=" * 50)
    print(f"TEST RESULTS: {passed} passed, {failed} failed")
    print("=" * 50)
    
    if failed == 0:
        print("🎉 All tests passed! Facial AI system is working correctly.")
    else:
        print(f"⚠️  {failed} test(s) failed. Please check the implementation.")
    
    return failed == 0

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
