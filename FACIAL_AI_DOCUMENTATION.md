# Facial AI System Documentation

## Overview

The Facial AI system is a comprehensive solution for enhancing podcast videos with intelligent face detection and dynamic zoom effects. It automatically detects faces in video content, identifies when speakers are talking using voice activity detection, and applies smooth zoom transitions to focus on the active speaker.

## Features

### Core Functionality
- **Real-time Face Detection**: Uses MediaPipe and OpenCV for accurate face detection
- **Voice Activity Detection**: Implements multiple VAD algorithms for robust speech detection
- **Dynamic Zoom Effects**: Applies smooth zoom transitions with configurable parameters
- **Speaker Identification**: Groups faces by spatial clustering to identify different speakers
- **Multi-speaker Support**: Handles videos with multiple speakers and switches focus automatically

### Technical Capabilities
- **High Accuracy**: Configurable detection sensitivity from 30% to 95%
- **Smooth Transitions**: Adjustable transition speeds from 0.1s to 2.0s
- **Flexible Zoom Levels**: Support for 1.0x to 3.0x zoom levels
- **Voice Threshold Control**: Customizable voice activity thresholds
- **Face Padding**: Configurable padding around detected faces

## Architecture

### Backend Components

#### 1. FacialAIProcessor (`facial_ai_processor.py`)
The main processing engine that handles:
- Face detection using MediaPipe Face Detection and Face Mesh
- Voice activity detection using librosa and pydub
- Audio extraction from video files using FFmpeg
- Correlation of voice activity with face detections
- Generation of zoom timelines and preview data

#### 2. API Endpoints (`main.py`)
Four main endpoints for facial AI functionality:

**POST /api/face-detection/analyze**
- Analyzes video for faces and speaking segments
- Returns detailed analysis data including face count, voice segments, and speaker timeline

**POST /api/face-detection/process**
- Processes video with dynamic face zoom effects
- Returns job ID for background processing tracking

**GET /api/face-detection/status/{job_id}**
- Checks processing status of background jobs
- Returns progress, status, and result URL when complete

**POST /api/face-detection/preview**
- Generates preview with face tracking overlay
- Returns sample frames with face detection information

#### 3. Data Models
- `FaceDetection`: Stores face detection results with timestamp, bounding box, and confidence
- `VoiceActivity`: Represents voice activity segments with start/end times
- `ZoomConfig`: Configuration parameters for zoom effects
- API request/response models for all endpoints

### Frontend Components

#### 1. FaceDetectionConfig Component (`src/components/FaceDetectionConfig.tsx`)
A comprehensive React component providing:
- Configuration interface for all zoom parameters
- Real-time parameter adjustment with sliders
- Analysis results visualization
- Preview frame display
- Processing status monitoring

#### 2. FacialAI Page (`src/pages/FacialAI.tsx`)
Main page component that:
- Handles file uploads and URL input
- Integrates with the configuration component
- Manages API calls and state
- Displays processing results and download options

## Configuration Parameters

### Zoom Level (1.0x - 3.0x)
- **1.0x - 1.2x**: Subtle zoom for minimal effect
- **1.2x - 1.5x**: Moderate zoom for balanced enhancement
- **1.5x - 2.0x**: Strong zoom for clear focus
- **2.0x - 3.0x**: Extreme zoom for dramatic effect

### Transition Speed (0.1s - 2.0s)
- **0.1s - 0.3s**: Very fast transitions
- **0.3s - 0.5s**: Fast transitions
- **0.5s - 0.8s**: Moderate transitions
- **0.8s - 2.0s**: Slow, smooth transitions

### Detection Sensitivity (30% - 95%)
- **30% - 50%**: Low sensitivity, fewer false positives
- **50% - 70%**: Medium sensitivity, balanced detection
- **70% - 80%**: High sensitivity, more detections
- **80% - 95%**: Very high sensitivity, maximum detection

### Voice Threshold (10% - 80%)
- **10% - 30%**: Detects quiet speech and whispers
- **30% - 50%**: Standard speech detection
- **50% - 80%**: Only loud, clear speech

### Face Padding (20px - 150px)
- **20px - 50px**: Tight framing around faces
- **50px - 100px**: Comfortable padding
- **100px - 150px**: Generous padding for context

## API Usage Examples

### Analyze Video with File Upload

```bash
curl -X POST "http://localhost:8000/api/face-detection/analyze" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -F "file=@podcast_video.mp4" \
  -F "request={\"config\":{\"zoom_level\":1.5,\"detection_sensitivity\":0.7}}"
```

### Process Video with URL

```bash
curl -X POST "http://localhost:8000/api/face-detection/process" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "video_url": "https://example.com/podcast.mp4",
    "config": {
      "zoom_level": 2.0,
      "transition_speed": 0.3,
      "detection_sensitivity": 0.8,
      "voice_threshold": 0.4,
      "padding": 75
    }
  }'
```

### Check Processing Status

```bash
curl -X GET "http://localhost:8000/api/face-detection/status/job-id-123" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### Generate Preview

```bash
curl -X POST "http://localhost:8000/api/face-detection/preview" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -F "file=@podcast_video.mp4" \
  -F "request={\"config\":{\"zoom_level\":1.8}}"
```

## Response Formats

### Analysis Response
```json
{
  "success": true,
  "message": "Video analysis completed successfully",
  "analysis_data": {
    "video_path": "/path/to/video.mp4",
    "duration": 300.5,
    "face_detections": 1250,
    "voice_segments": 15,
    "speaker_segments": [
      {
        "start_time": 10.5,
        "end_time": 25.3,
        "speaker_id": 0,
        "face_bbox": [100, 150, 80, 100],
        "confidence": 0.85
      }
    ],
    "zoom_timeline": [
      {
        "start_time": 10.5,
        "end_time": 25.3,
        "zoom_level": 1.5,
        "target_bbox": [100, 150, 80, 100],
        "transition_speed": 0.5,
        "speaker_id": 0
      }
    ]
  }
}
```

### Processing Status Response
```json
{
  "job_id": "uuid-job-id",
  "status": "processing",
  "progress": 0.65,
  "result_url": null,
  "error": null,
  "created_at": "2024-01-01T12:00:00Z",
  "completed_at": null
}
```

## Testing

### Unit Tests (`test_facial_ai.py`)
Comprehensive test suite covering:
- FacialAIProcessor initialization and configuration
- Face detection accuracy and performance
- Voice activity detection algorithms
- Speaker correlation and grouping
- Timeline generation and preview creation

### API Tests (`test_facial_ai_api.py`)
API endpoint testing including:
- Authentication and authorization
- File upload and URL processing
- Background job management
- Error handling and validation
- Response format verification

### Running Tests

```bash
# Run unit tests
cd backend
python -m pytest test_facial_ai.py -v

# Run API tests
python -m pytest test_facial_ai_api.py -v

# Run all facial AI tests
python -m pytest test_facial_ai*.py -v
```

## Performance Considerations

### Processing Speed
- **Face Detection**: ~5-10 FPS on modern hardware
- **Voice Analysis**: Real-time processing for most audio formats
- **Video Processing**: Depends on video length and complexity
- **Memory Usage**: ~2-4GB RAM for typical podcast videos

### Optimization Tips
1. **Lower Detection Sensitivity**: Reduces processing time
2. **Skip Frame Processing**: Process every 5th frame for speed
3. **Audio Preprocessing**: Use lower sample rates for VAD
4. **Batch Processing**: Process multiple videos in parallel
5. **GPU Acceleration**: Use CUDA-enabled OpenCV for faster processing

## Integration with SmartClips

The Facial AI system integrates seamlessly with the existing SmartClips infrastructure:

- **Authentication**: Uses existing JWT token system
- **File Storage**: Leverages Cloudinary for video storage
- **Database**: Stores job status and user associations
- **Background Processing**: Uses FastAPI BackgroundTasks
- **Frontend**: Integrates with existing React/TypeScript components

## Future Enhancements

### Planned Features
1. **Real-time Processing**: Live streaming support
2. **Advanced Speaker Recognition**: ML-based speaker identification
3. **Emotion Detection**: Facial expression analysis
4. **Custom Zoom Patterns**: User-defined zoom behaviors
5. **Multi-camera Support**: Handle multiple camera angles
6. **Export Presets**: Platform-specific optimization presets

### Technical Improvements
1. **GPU Acceleration**: CUDA support for faster processing
2. **Distributed Processing**: Multi-server processing support
3. **Advanced VAD**: Deep learning-based voice activity detection
4. **Face Recognition**: Persistent speaker identification across videos
5. **Quality Metrics**: Automatic quality assessment and optimization

## Troubleshooting

### Common Issues

**Face Detection Not Working**
- Check video quality and lighting
- Adjust detection sensitivity
- Ensure faces are clearly visible
- Verify MediaPipe installation

**Voice Activity Detection Issues**
- Check audio quality and volume levels
- Adjust voice threshold settings
- Ensure audio track exists in video
- Verify librosa and pydub installation

**Processing Failures**
- Check available disk space
- Verify FFmpeg installation
- Monitor memory usage
- Check video format compatibility

**API Authentication Errors**
- Verify JWT token validity
- Check user permissions
- Ensure proper header format
- Validate token expiration

### Debug Mode

Enable debug logging by setting the log level:

```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

This provides detailed information about:
- Face detection results
- Voice activity segments
- Processing pipeline steps
- Error stack traces
- Performance metrics

## Support and Maintenance

For technical support or feature requests, please refer to the main SmartClips documentation or contact the development team. Regular updates and improvements are planned as part of the ongoing Crayo AI competitor roadmap.
