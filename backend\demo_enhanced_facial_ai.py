#!/usr/bin/env python3
"""
Enhanced Facial AI Demo Script
Demonstrates the complete enhanced functionality with dynamic speaker switching,
improved framing, and professional video output generation.
"""

import os
import sys
import time
import logging
from pathlib import Path

# Add backend to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from facial_ai_processor import FacialAIProcessor, ZoomConfig

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def demo_enhanced_facial_ai():
    """Demonstrate enhanced facial AI functionality"""
    
    logger.info("🚀 Enhanced Facial AI Demo")
    logger.info("=" * 50)
    
    # Look for test video
    test_videos = [
        "videos/videoplayback (1).mp4",
        "videos/videoplayback (2).mp4",
        "../test_videos/sample.mp4",
        "test_video.mp4"
    ]
    
    test_video = None
    for video_path in test_videos:
        if os.path.exists(video_path):
            test_video = video_path
            break
    
    if not test_video:
        logger.error("❌ No test video found. Please provide a test video.")
        logger.info("Expected locations:")
        for path in test_videos:
            logger.info(f"  - {path}")
        return False
    
    logger.info(f"🎥 Using test video: {test_video}")
    
    # Create output directory
    output_dir = Path("demo_outputs")
    output_dir.mkdir(exist_ok=True)
    
    # Initialize processor
    processor = FacialAIProcessor()
    
    # Demo configurations
    demo_configs = {
        "Professional Podcast": ZoomConfig(
            zoom_level=2.0,
            include_torso=True,
            headroom_ratio=0.15,
            torso_ratio=0.6,
            speaker_switch_threshold=0.5,
            transition_smoothness=0.4,
            detection_sensitivity=0.7,
            voice_threshold=0.3
        ),
        "Dynamic Interview": ZoomConfig(
            zoom_level=1.8,
            include_torso=True,
            headroom_ratio=0.12,
            torso_ratio=0.8,
            speaker_switch_threshold=0.3,
            transition_smoothness=0.6,
            detection_sensitivity=0.6,
            voice_threshold=0.25
        ),
        "Presentation Style": ZoomConfig(
            zoom_level=1.6,
            include_torso=True,
            headroom_ratio=0.2,
            torso_ratio=0.5,
            speaker_switch_threshold=0.7,
            transition_smoothness=0.3,
            detection_sensitivity=0.8,
            voice_threshold=0.35
        )
    }
    
    results = {}
    
    for config_name, config in demo_configs.items():
        logger.info(f"\n🎬 Processing: {config_name}")
        logger.info("-" * 30)
        
        output_path = output_dir / f"enhanced_{config_name.lower().replace(' ', '_')}.mp4"
        
        try:
            # Process video with enhanced functionality
            start_time = time.time()
            
            result = processor.generate_complete_processed_video(
                test_video,
                str(output_path),
                config
            )
            
            processing_time = time.time() - start_time
            
            if result["success"]:
                logger.info(f"✅ {config_name} completed successfully!")
                logger.info(f"⏱️ Processing time: {processing_time:.2f} seconds")
                logger.info(f"📁 Output: {output_path}")
                
                # Display analysis results
                analysis = result.get("analysis", {})
                logger.info(f"🎭 Speaker segments: {analysis.get('speaker_segments', 0)}")
                logger.info(f"🎯 Zoom effects: {analysis.get('zoom_segments', 0)}")
                logger.info(f"👥 Face detections: {analysis.get('face_detections', 0)}")
                logger.info(f"🗣️ Voice segments: {analysis.get('voice_segments', 0)}")
                
                # Display enhancements
                enhancements = result.get("enhancements", {})
                logger.info("🔧 Applied enhancements:")
                for enhancement, enabled in enhancements.items():
                    status = "✅" if enabled else "❌"
                    logger.info(f"  {status} {enhancement.replace('_', ' ').title()}")
                
                results[config_name] = {
                    "success": True,
                    "output_path": str(output_path),
                    "processing_time": processing_time,
                    "analysis": analysis,
                    "enhancements": enhancements
                }
                
            else:
                logger.error(f"❌ {config_name} failed: {result.get('error', 'Unknown error')}")
                results[config_name] = {
                    "success": False,
                    "error": result.get("error", "Unknown error"),
                    "processing_time": processing_time
                }
                
        except Exception as e:
            logger.error(f"❌ Error processing {config_name}: {str(e)}")
            results[config_name] = {
                "success": False,
                "error": str(e),
                "processing_time": time.time() - start_time
            }
    
    # Generate demo summary
    logger.info("\n📊 Demo Summary")
    logger.info("=" * 50)
    
    successful_demos = [name for name, result in results.items() if result["success"]]
    failed_demos = [name for name, result in results.items() if not result["success"]]
    
    logger.info(f"Total configurations tested: {len(demo_configs)}")
    logger.info(f"Successful demos: {len(successful_demos)}")
    logger.info(f"Failed demos: {len(failed_demos)}")
    
    if successful_demos:
        logger.info("\n✅ Successful configurations:")
        for name in successful_demos:
            result = results[name]
            logger.info(f"  - {name}: {result['processing_time']:.2f}s")
            logger.info(f"    Output: {result['output_path']}")
    
    if failed_demos:
        logger.info("\n❌ Failed configurations:")
        for name in failed_demos:
            result = results[name]
            logger.info(f"  - {name}: {result.get('error', 'Unknown error')}")
    
    # Feature demonstration summary
    logger.info("\n🎯 Enhanced Features Demonstrated:")
    logger.info("  ✅ Dynamic Speaker Switching - Automatically switches focus between speakers")
    logger.info("  ✅ Professional Framing - Includes torso and proper headroom")
    logger.info("  ✅ Smooth Transitions - Eased transitions between speakers")
    logger.info("  ✅ Complete Video Output - Generates full processed video files")
    logger.info("  ✅ Quality Verification - Validates output video quality")
    logger.info("  ✅ Multiple Configurations - Different settings for various use cases")
    
    # Usage recommendations
    logger.info("\n💡 Usage Recommendations:")
    logger.info("  🎙️ Professional Podcast: Best for formal podcast recordings")
    logger.info("  🎤 Dynamic Interview: Ideal for fast-paced interviews")
    logger.info("  📺 Presentation Style: Perfect for educational content")
    
    logger.info(f"\n📁 All demo outputs saved to: {output_dir}")
    
    return len(successful_demos) > 0

def verify_demo_outputs():
    """Verify that demo outputs are valid"""
    logger.info("\n🔍 Verifying demo outputs...")
    
    output_dir = Path("demo_outputs")
    if not output_dir.exists():
        logger.warning("No demo outputs directory found")
        return False
    
    video_files = list(output_dir.glob("*.mp4"))
    
    if not video_files:
        logger.warning("No video files found in demo outputs")
        return False
    
    logger.info(f"Found {len(video_files)} demo video files:")
    
    for video_file in video_files:
        file_size = video_file.stat().st_size
        size_mb = file_size / (1024 * 1024)
        
        logger.info(f"  📹 {video_file.name}: {size_mb:.2f} MB")
        
        # Basic validation - file should be larger than 1MB
        if size_mb < 1.0:
            logger.warning(f"    ⚠️ File seems too small: {size_mb:.2f} MB")
        else:
            logger.info(f"    ✅ File size looks good")
    
    return True

def main():
    """Main demo execution"""
    try:
        # Run the demo
        success = demo_enhanced_facial_ai()
        
        if success:
            # Verify outputs
            verify_demo_outputs()
            
            logger.info("\n🎉 Enhanced Facial AI Demo completed successfully!")
            logger.info("🎬 You can now play the generated videos to see the enhancements:")
            logger.info("   - Dynamic speaker switching in action")
            logger.info("   - Professional torso+head framing")
            logger.info("   - Smooth transitions between speakers")
            logger.info("   - High-quality video output")
            
        else:
            logger.error("❌ Demo failed. Check the logs for details.")
            
    except KeyboardInterrupt:
        logger.info("\n⏹️ Demo interrupted by user")
    except Exception as e:
        logger.error(f"❌ Unexpected error during demo: {str(e)}")

if __name__ == "__main__":
    main()
