#!/usr/bin/env python3
"""
Enhanced Facial AI Testing Suite
Tests dynamic speaker switching, improved framing, and complete video output generation
"""

import os
import sys
import time
import logging
import cv2
import numpy as np
from pathlib import Path

# Add backend to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from facial_ai_processor import FacialAIProcessor, ZoomConfig

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('enhanced_facial_ai_test.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class EnhancedFacialAITester:
    """Comprehensive testing suite for enhanced facial AI functionality"""
    
    def __init__(self):
        self.processor = FacialAIProcessor()
        self.test_results = {}
        self.output_dir = Path("enhanced_test_outputs")
        self.output_dir.mkdir(exist_ok=True)
        
    def run_comprehensive_tests(self, video_path: str) -> Dict[str, Any]:
        """Run all enhanced facial AI tests"""
        logger.info("🚀 Starting Enhanced Facial AI Comprehensive Tests")
        logger.info("=" * 60)
        
        if not os.path.exists(video_path):
            logger.error(f"❌ Test video not found: {video_path}")
            return {"success": False, "error": "Test video not found"}
        
        # Test configurations
        test_configs = {
            "basic_enhanced": ZoomConfig(
                zoom_level=1.8,
                include_torso=True,
                headroom_ratio=0.15,
                torso_ratio=0.6,
                speaker_switch_threshold=0.5,
                transition_smoothness=0.3
            ),
            "professional": ZoomConfig(
                zoom_level=2.2,
                include_torso=True,
                headroom_ratio=0.2,
                torso_ratio=0.8,
                speaker_switch_threshold=0.3,
                transition_smoothness=0.5
            ),
            "dynamic_switching": ZoomConfig(
                zoom_level=1.6,
                include_torso=True,
                headroom_ratio=0.12,
                torso_ratio=0.5,
                speaker_switch_threshold=0.2,
                transition_smoothness=0.8
            )
        }
        
        all_results = {}
        
        for config_name, config in test_configs.items():
            logger.info(f"\n🧪 Testing Configuration: {config_name}")
            logger.info("-" * 40)
            
            result = self._test_configuration(video_path, config, config_name)
            all_results[config_name] = result
            
            if result["success"]:
                logger.info(f"✅ {config_name} test completed successfully")
            else:
                logger.error(f"❌ {config_name} test failed: {result.get('error', 'Unknown error')}")
        
        # Generate comprehensive report
        report = self._generate_test_report(all_results, video_path)
        
        logger.info("\n📊 Test Summary")
        logger.info("=" * 60)
        logger.info(f"Total configurations tested: {len(test_configs)}")
        logger.info(f"Successful tests: {sum(1 for r in all_results.values() if r['success'])}")
        logger.info(f"Failed tests: {sum(1 for r in all_results.values() if not r['success'])}")
        
        return report
    
    def _test_configuration(self, video_path: str, config: ZoomConfig, config_name: str) -> Dict[str, Any]:
        """Test a specific configuration"""
        try:
            output_path = self.output_dir / f"enhanced_{config_name}_output.mp4"
            
            # Test complete video processing
            logger.info(f"🎬 Processing video with {config_name} configuration...")
            start_time = time.time()
            
            result = self.processor.generate_complete_processed_video(
                video_path, 
                str(output_path), 
                config
            )
            
            processing_time = time.time() - start_time
            
            if result["success"]:
                # Verify output quality
                verification = self._verify_enhanced_features(str(output_path), video_path, config)
                
                return {
                    "success": True,
                    "config": config.__dict__,
                    "processing_time": processing_time,
                    "output_path": str(output_path),
                    "analysis": result.get("analysis", {}),
                    "verification": verification,
                    "enhancements": result.get("enhancements", {})
                }
            else:
                return {
                    "success": False,
                    "error": result.get("error", "Unknown processing error"),
                    "config": config.__dict__,
                    "processing_time": processing_time
                }
                
        except Exception as e:
            logger.error(f"Error testing {config_name}: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "config": config.__dict__
            }
    
    def _verify_enhanced_features(self, output_path: str, input_path: str, config: ZoomConfig) -> Dict[str, Any]:
        """Verify enhanced features in the output video"""
        verification = {
            "file_exists": os.path.exists(output_path),
            "file_size_reasonable": False,
            "video_playable": False,
            "duration_preserved": False,
            "quality_metrics": {}
        }
        
        try:
            if verification["file_exists"]:
                # Check file size
                output_size = os.path.getsize(output_path)
                input_size = os.path.getsize(input_path)
                size_ratio = output_size / input_size
                verification["file_size_reasonable"] = 0.3 <= size_ratio <= 3.0
                verification["quality_metrics"]["size_ratio"] = size_ratio
                
                # Check video properties
                cap_input = cv2.VideoCapture(input_path)
                cap_output = cv2.VideoCapture(output_path)
                
                if cap_input.isOpened() and cap_output.isOpened():
                    verification["video_playable"] = True
                    
                    input_frames = int(cap_input.get(cv2.CAP_PROP_FRAME_COUNT))
                    output_frames = int(cap_output.get(cv2.CAP_PROP_FRAME_COUNT))
                    frame_diff = abs(input_frames - output_frames)
                    
                    verification["duration_preserved"] = frame_diff <= 10
                    verification["quality_metrics"]["frame_difference"] = frame_diff
                    verification["quality_metrics"]["input_frames"] = input_frames
                    verification["quality_metrics"]["output_frames"] = output_frames
                
                cap_input.release()
                cap_output.release()
                
        except Exception as e:
            verification["error"] = str(e)
        
        return verification
    
    def _generate_test_report(self, results: Dict[str, Any], video_path: str) -> Dict[str, Any]:
        """Generate comprehensive test report"""
        successful_tests = [name for name, result in results.items() if result["success"]]
        failed_tests = [name for name, result in results.items() if not result["success"]]
        
        report = {
            "test_summary": {
                "input_video": video_path,
                "total_configurations": len(results),
                "successful_tests": len(successful_tests),
                "failed_tests": len(failed_tests),
                "success_rate": len(successful_tests) / len(results) * 100
            },
            "successful_configurations": successful_tests,
            "failed_configurations": failed_tests,
            "detailed_results": results,
            "recommendations": self._generate_recommendations(results)
        }
        
        # Save report to file
        report_path = self.output_dir / "enhanced_test_report.json"
        import json
        with open(report_path, 'w') as f:
            json.dump(report, f, indent=2, default=str)
        
        logger.info(f"📄 Detailed test report saved to: {report_path}")
        
        return report
    
    def _generate_recommendations(self, results: Dict[str, Any]) -> List[str]:
        """Generate recommendations based on test results"""
        recommendations = []
        
        successful_results = [r for r in results.values() if r["success"]]
        
        if successful_results:
            # Find best performing configuration
            best_config = min(successful_results, key=lambda x: x.get("processing_time", float('inf')))
            recommendations.append(f"Best performing configuration based on processing time: {best_config.get('config', {})}")
            
            # Check for consistent features
            all_have_torso = all(r.get("enhancements", {}).get("professional_framing", False) for r in successful_results)
            if all_have_torso:
                recommendations.append("All successful tests used professional torso+head framing")
            
            all_have_transitions = all(r.get("enhancements", {}).get("smooth_transitions", False) for r in successful_results)
            if all_have_transitions:
                recommendations.append("All successful tests implemented smooth transitions")
        
        if len(successful_results) < len(results):
            recommendations.append("Some configurations failed - check error logs for debugging")
        
        return recommendations

def main():
    """Main test execution"""
    # Look for test video
    test_videos = [
        "videos/videoplayback (1).mp4",
        "videos/videoplayback (2).mp4",
        "../test_videos/sample.mp4",
        "test_video.mp4"
    ]
    
    test_video = None
    for video_path in test_videos:
        if os.path.exists(video_path):
            test_video = video_path
            break
    
    if not test_video:
        logger.error("❌ No test video found. Please provide a test video.")
        logger.info("Expected locations:")
        for path in test_videos:
            logger.info(f"  - {path}")
        return
    
    logger.info(f"🎥 Using test video: {test_video}")
    
    # Run comprehensive tests
    tester = EnhancedFacialAITester()
    results = tester.run_comprehensive_tests(test_video)
    
    if results["test_summary"]["success_rate"] > 80:
        logger.info("🎉 Enhanced Facial AI tests completed successfully!")
    else:
        logger.warning("⚠️ Some tests failed. Check the detailed report for more information.")

if __name__ == "__main__":
    main()
