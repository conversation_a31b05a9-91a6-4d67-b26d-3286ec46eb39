"""
Unit tests for the Facial AI Processor
"""

import pytest
import asyncio
import tempfile
import os
import numpy as np
import cv2
from unittest.mock import Mock, patch, AsyncMock
from facial_ai_processor import (
    FacialAIProcessor, 
    ZoomConfig, 
    FaceDetection, 
    VoiceActivity
)

class TestFacialAIProcessor:
    """Test suite for FacialAIProcessor"""
    
    @pytest.fixture
    def processor(self):
        """Create a FacialAIProcessor instance for testing"""
        return FacialAIProcessor(faces_api_key="test_key")
    
    @pytest.fixture
    def sample_config(self):
        """Create a sample ZoomConfig for testing"""
        return ZoomConfig(
            zoom_level=1.5,
            transition_speed=0.5,
            detection_sensitivity=0.7,
            voice_threshold=0.3,
            padding=50
        )
    
    @pytest.fixture
    def sample_video_path(self):
        """Create a temporary video file for testing"""
        # Create a simple test video using OpenCV
        temp_file = tempfile.mktemp(suffix=".mp4")
        
        # Create a simple 5-second video with a moving rectangle (simulating a face)
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        out = cv2.VideoWriter(temp_file, fourcc, 30.0, (640, 480))
        
        for frame_num in range(150):  # 5 seconds at 30 fps
            # Create a black frame
            frame = np.zeros((480, 640, 3), dtype=np.uint8)
            
            # Add a moving white rectangle (simulating a face)
            x = 200 + int(50 * np.sin(frame_num * 0.1))
            y = 200 + int(30 * np.cos(frame_num * 0.1))
            cv2.rectangle(frame, (x, y), (x + 100, y + 120), (255, 255, 255), -1)
            
            out.write(frame)
        
        out.release()
        yield temp_file
        
        # Cleanup
        if os.path.exists(temp_file):
            os.remove(temp_file)
    
    def test_processor_initialization(self, processor):
        """Test that the processor initializes correctly"""
        assert processor.faces_api_key == "test_key"
        assert processor.faces_db_url == "https://faces.mpdl.mpg.de/imeji/user?email=ivaturi.anish%40gmail.com"
        assert processor.face_detection is not None
        assert processor.face_mesh is not None
    
    def test_zoom_config_defaults(self):
        """Test ZoomConfig default values"""
        config = ZoomConfig()
        assert config.zoom_level == 1.5
        assert config.transition_speed == 0.5
        assert config.detection_sensitivity == 0.7
        assert config.voice_threshold == 0.3
        assert config.padding == 50
    
    def test_face_detection_dataclass(self):
        """Test FaceDetection dataclass"""
        face = FaceDetection(
            timestamp=1.5,
            bbox=(100, 100, 50, 60),
            confidence=0.85,
            landmarks=[(110, 110), (120, 115)],
            speaker_id=1
        )
        
        assert face.timestamp == 1.5
        assert face.bbox == (100, 100, 50, 60)
        assert face.confidence == 0.85
        assert face.landmarks == [(110, 110), (120, 115)]
        assert face.speaker_id == 1
    
    def test_voice_activity_dataclass(self):
        """Test VoiceActivity dataclass"""
        voice = VoiceActivity(
            start_time=2.0,
            end_time=5.0,
            confidence=0.9,
            speaker_id=0
        )
        
        assert voice.start_time == 2.0
        assert voice.end_time == 5.0
        assert voice.confidence == 0.9
        assert voice.speaker_id == 0
    
    @pytest.mark.asyncio
    async def test_extract_audio(self, processor, sample_video_path):
        """Test audio extraction from video"""
        audio_path = await processor._extract_audio(sample_video_path)
        
        assert audio_path.endswith('.wav')
        assert os.path.exists(audio_path)
        
        # Cleanup
        if os.path.exists(audio_path):
            os.remove(audio_path)
    
    @pytest.mark.asyncio
    async def test_detect_faces_in_video(self, processor, sample_video_path, sample_config):
        """Test face detection in video"""
        with patch.object(processor.face_detection, 'process') as mock_process:
            # Mock MediaPipe face detection results
            mock_detection = Mock()
            mock_detection.location_data.relative_bounding_box.xmin = 0.3
            mock_detection.location_data.relative_bounding_box.ymin = 0.2
            mock_detection.location_data.relative_bounding_box.width = 0.2
            mock_detection.location_data.relative_bounding_box.height = 0.25
            mock_detection.score = [0.85]
            
            mock_results = Mock()
            mock_results.detections = [mock_detection]
            mock_process.return_value = mock_results
            
            face_detections = await processor._detect_faces_in_video(sample_video_path, sample_config)
            
            assert len(face_detections) > 0
            assert all(isinstance(detection, FaceDetection) for detection in face_detections)
            assert all(detection.confidence >= sample_config.detection_sensitivity for detection in face_detections)
    
    @pytest.mark.asyncio
    async def test_detect_voice_activity(self, processor, sample_config):
        """Test voice activity detection"""
        # Create a temporary audio file with some audio data
        temp_audio = tempfile.mktemp(suffix=".wav")
        
        # Generate a simple sine wave audio (simulating speech)
        import soundfile as sf
        sample_rate = 16000
        duration = 3.0
        t = np.linspace(0, duration, int(sample_rate * duration))
        
        # Create audio with alternating loud and quiet sections
        audio_data = np.zeros_like(t)
        audio_data[0:8000] = 0.5 * np.sin(2 * np.pi * 440 * t[0:8000])  # Loud section
        audio_data[16000:24000] = 0.1 * np.sin(2 * np.pi * 440 * t[16000:24000])  # Quiet section
        audio_data[32000:40000] = 0.6 * np.sin(2 * np.pi * 440 * t[32000:40000])  # Loud section
        
        sf.write(temp_audio, audio_data, sample_rate)
        
        try:
            voice_activities = await processor._detect_voice_activity(temp_audio, sample_config)
            
            assert len(voice_activities) > 0
            assert all(isinstance(activity, VoiceActivity) for activity in voice_activities)
            assert all(activity.start_time < activity.end_time for activity in voice_activities)
            
        finally:
            if os.path.exists(temp_audio):
                os.remove(temp_audio)
    
    def test_merge_voice_segments(self, processor):
        """Test merging of overlapping voice segments"""
        segments = [
            VoiceActivity(start_time=1.0, end_time=3.0, confidence=0.8),
            VoiceActivity(start_time=2.5, end_time=4.0, confidence=0.9),  # Overlapping
            VoiceActivity(start_time=6.0, end_time=8.0, confidence=0.7),  # Separate
            VoiceActivity(start_time=7.5, end_time=9.0, confidence=0.85)  # Close enough to merge
        ]
        
        merged = processor._merge_voice_segments(segments)
        
        assert len(merged) == 2  # Should merge overlapping segments
        assert merged[0].start_time == 1.0
        assert merged[0].end_time == 4.0
        assert merged[1].start_time == 6.0
        assert merged[1].end_time == 9.0
    
    def test_group_faces_by_speaker(self, processor):
        """Test grouping faces by speaker based on spatial clustering"""
        faces = [
            FaceDetection(timestamp=1.0, bbox=(100, 100, 50, 60), confidence=0.8),
            FaceDetection(timestamp=1.5, bbox=(105, 105, 50, 60), confidence=0.85),  # Same speaker
            FaceDetection(timestamp=2.0, bbox=(300, 150, 50, 60), confidence=0.9),   # Different speaker
            FaceDetection(timestamp=2.5, bbox=(305, 155, 50, 60), confidence=0.87),  # Same as third
        ]
        
        grouped = processor._group_faces_by_speaker(faces)
        
        assert len(grouped) == 2  # Should identify 2 speakers
        assert len(grouped[0]) == 2  # First speaker has 2 detections
        assert len(grouped[1]) == 2  # Second speaker has 2 detections
    
    def test_calculate_average_bbox(self, processor):
        """Test calculation of average bounding box"""
        faces = [
            FaceDetection(timestamp=1.0, bbox=(100, 100, 50, 60), confidence=0.8),
            FaceDetection(timestamp=1.5, bbox=(110, 105, 50, 60), confidence=0.85),
            FaceDetection(timestamp=2.0, bbox=(105, 110, 50, 60), confidence=0.9),
        ]
        
        avg_bbox = processor._calculate_average_bbox(faces)
        
        assert avg_bbox == (105, 105, 50, 60)  # Average of the three bboxes
    
    def test_correlate_voice_and_faces(self, processor):
        """Test correlation of voice activity with face detections"""
        voice_activities = [
            VoiceActivity(start_time=1.0, end_time=3.0, confidence=0.8),
            VoiceActivity(start_time=5.0, end_time=7.0, confidence=0.9),
        ]
        
        face_detections = [
            FaceDetection(timestamp=1.5, bbox=(100, 100, 50, 60), confidence=0.8),
            FaceDetection(timestamp=2.0, bbox=(105, 105, 50, 60), confidence=0.85),
            FaceDetection(timestamp=5.5, bbox=(300, 150, 50, 60), confidence=0.9),
            FaceDetection(timestamp=6.0, bbox=(305, 155, 50, 60), confidence=0.87),
        ]
        
        speaker_segments = processor._correlate_voice_and_faces(voice_activities, face_detections)
        
        assert len(speaker_segments) == 2  # Should create 2 speaker segments
        assert speaker_segments[0]["start_time"] == 1.0
        assert speaker_segments[0]["end_time"] == 3.0
        assert speaker_segments[1]["start_time"] == 5.0
        assert speaker_segments[1]["end_time"] == 7.0
    
    def test_generate_zoom_timeline(self, processor, sample_config):
        """Test generation of zoom timeline"""
        speaker_segments = [
            {
                "start_time": 1.0,
                "end_time": 3.0,
                "speaker_id": 0,
                "face_bbox": (100, 100, 50, 60),
                "confidence": 0.8
            },
            {
                "start_time": 5.0,
                "end_time": 7.0,
                "speaker_id": 1,
                "face_bbox": (300, 150, 50, 60),
                "confidence": 0.9
            }
        ]
        
        zoom_timeline = processor._generate_zoom_timeline(speaker_segments, sample_config)
        
        assert len(zoom_timeline) == 2
        assert zoom_timeline[0]["zoom_level"] == sample_config.zoom_level
        assert zoom_timeline[0]["transition_speed"] == sample_config.transition_speed
        assert zoom_timeline[0]["target_bbox"] == (100, 100, 50, 60)
        assert zoom_timeline[1]["target_bbox"] == (300, 150, 50, 60)
    
    @pytest.mark.asyncio
    async def test_analyze_video_integration(self, processor, sample_video_path, sample_config):
        """Test the complete video analysis pipeline"""
        with patch.object(processor, '_extract_audio') as mock_extract_audio, \
             patch.object(processor, '_detect_voice_activity') as mock_detect_voice, \
             patch.object(processor, '_detect_faces_in_video') as mock_detect_faces:
            
            # Mock the audio extraction
            mock_extract_audio.return_value = "mock_audio.wav"
            
            # Mock voice activity detection
            mock_detect_voice.return_value = [
                VoiceActivity(start_time=1.0, end_time=3.0, confidence=0.8)
            ]
            
            # Mock face detection
            mock_detect_faces.return_value = [
                FaceDetection(timestamp=1.5, bbox=(100, 100, 50, 60), confidence=0.8),
                FaceDetection(timestamp=2.0, bbox=(105, 105, 50, 60), confidence=0.85)
            ]
            
            analysis_result = await processor.analyze_video(sample_video_path, sample_config)
            
            assert analysis_result["video_path"] == sample_video_path
            assert "duration" in analysis_result
            assert "face_detections" in analysis_result
            assert "voice_segments" in analysis_result
            assert "speaker_segments" in analysis_result
            assert "zoom_timeline" in analysis_result
            assert "config" in analysis_result
            assert "timestamp" in analysis_result
    
    @pytest.mark.asyncio
    async def test_generate_preview(self, processor, sample_video_path, sample_config):
        """Test preview generation"""
        with patch.object(processor, 'analyze_video') as mock_analyze:
            # Mock analysis result
            mock_analyze.return_value = {
                "video_path": sample_video_path,
                "duration": 5.0,
                "face_detections": 10,
                "voice_segments": 3,
                "speaker_segments": [
                    {
                        "start_time": 1.0,
                        "end_time": 3.0,
                        "speaker_id": 0,
                        "face_bbox": (100, 100, 50, 60),
                        "confidence": 0.8
                    }
                ],
                "zoom_timeline": [],
                "config": sample_config.__dict__,
                "timestamp": "2024-01-01T00:00:00"
            }
            
            with patch.object(processor, '_generate_preview_frames') as mock_preview_frames:
                mock_preview_frames.return_value = [
                    {
                        "timestamp": 1.0,
                        "frame_number": 30,
                        "faces": [{"speaker_id": 0}],
                        "has_speech": True
                    }
                ]
                
                preview_data = await processor.generate_preview(sample_video_path, sample_config)
                
                assert "video_path" in preview_data
                assert "analysis" in preview_data
                assert "preview_frames" in preview_data
                assert "total_frames" in preview_data
                assert "timestamp" in preview_data


if __name__ == "__main__":
    # Run the tests
    pytest.main([__file__, "-v"])
