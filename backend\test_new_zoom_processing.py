"""
Test script to process video with new zoom implementation
"""

import os
import logging
from pathlib import Path
from facial_ai_processor import FacialAIProcessor, ZoomConfig

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('new_zoom_test.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def test_new_zoom_processing():
    """Test the new zoom processing implementation"""
    
    logger.info("Testing New Zoom Processing Implementation")
    logger.info("=" * 60)
    
    # Find original video
    videos_dir = Path("videos")
    original_video = None
    
    for video_file in videos_dir.glob("*.mp4"):
        if "processed_" not in video_file.name:
            original_video = str(video_file)
            break
    
    if not original_video:
        logger.error("No original video found")
        return False
    
    logger.info(f"Processing video: {original_video}")
    
    # Initialize processor
    processor = FacialAIProcessor()
    config = ZoomConfig(zoom_level=2.0, detection_sensitivity=0.6)
    
    # Process video with zoom effects
    output_video = "videos/processed_videoplayback (1)_zoom.mp4"
    
    logger.info("Starting video processing with zoom effects...")
    result_path = processor.process_video_with_face_zoom(original_video, output_video, config)
    
    if os.path.exists(result_path):
        file_size = os.path.getsize(result_path) / (1024 * 1024)
        logger.info(f"SUCCESS: Processed video created")
        logger.info(f"  - Output: {result_path}")
        logger.info(f"  - Size: {file_size:.1f} MB")
        return True
    else:
        logger.error("FAILED: Processed video not created")
        return False

if __name__ == "__main__":
    success = test_new_zoom_processing()
    exit(0 if success else 1)
