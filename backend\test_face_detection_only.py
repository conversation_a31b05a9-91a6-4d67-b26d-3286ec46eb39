"""
Test just the face detection functionality without audio processing
"""

import os
import tempfile
import cv2
import numpy as np
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_test_video_with_face():
    """Create a test video with a clear face-like object"""
    temp_file = tempfile.mktemp(suffix=".mp4")
    logger.info(f"Creating test video: {temp_file}")
    
    # Video parameters
    fps = 30
    width, height = 640, 480
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    out = cv2.VideoWriter(temp_file, fourcc, fps, (width, height))
    
    # Create 2 seconds of video with a clear face
    for frame_num in range(60):
        # Create background
        frame = np.random.randint(20, 60, (height, width, 3), dtype=np.uint8)
        
        # Add a realistic face-like object
        center_x = 320  # Center of frame
        center_y = 240
        
        # Draw face oval (skin color)
        cv2.ellipse(frame, (center_x, center_y), (80, 100), 0, 0, 360, (220, 180, 160), -1)
        
        # Add facial features
        # Eyes
        cv2.circle(frame, (center_x - 25, center_y - 30), 12, (50, 50, 50), -1)
        cv2.circle(frame, (center_x + 25, center_y - 30), 12, (50, 50, 50), -1)
        
        # Eye pupils
        cv2.circle(frame, (center_x - 25, center_y - 30), 6, (0, 0, 0), -1)
        cv2.circle(frame, (center_x + 25, center_y - 30), 6, (0, 0, 0), -1)
        
        # Nose
        cv2.circle(frame, (center_x, center_y), 8, (180, 140, 120), -1)
        
        # Mouth
        cv2.ellipse(frame, (center_x, center_y + 35), (20, 10), 0, 0, 180, (100, 50, 50), -1)
        
        out.write(frame)
    
    out.release()
    logger.info(f"Test video created: {os.path.getsize(temp_file)} bytes")
    return temp_file

def test_face_detection_directly():
    """Test face detection directly without full analysis"""
    logger.info("Testing face detection directly...")
    
    try:
        from facial_ai_processor import FacialAIProcessor, ZoomConfig
        
        processor = FacialAIProcessor()
        config = ZoomConfig(detection_sensitivity=0.5)  # Lower sensitivity for testing
        
        # Create test video
        video_path = create_test_video_with_face()
        
        # Test face detection method directly
        logger.info("Running face detection...")
        face_detections = processor._detect_faces_in_video(video_path, config)
        
        logger.info(f"✅ Face detection completed")
        logger.info(f"  - Detected faces: {len(face_detections)}")
        
        for i, detection in enumerate(face_detections[:5]):  # Show first 5
            logger.info(f"  - Face {i+1}: time={detection.timestamp:.2f}s, confidence={detection.confidence:.2f}")
            logger.info(f"    bbox: {detection.bbox}")
        
        # Clean up
        os.remove(video_path)
        
        if len(face_detections) > 0:
            logger.info("🎉 SUCCESS: Face detection is working!")
            return True
        else:
            logger.warning("⚠️  WARNING: No faces detected")
            return False
            
    except Exception as e:
        logger.error(f"❌ Face detection test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_zoom_timeline_generation():
    """Test zoom timeline generation with mock data"""
    logger.info("Testing zoom timeline generation...")
    
    try:
        from facial_ai_processor import FacialAIProcessor, ZoomConfig, FaceDetection, VoiceActivity
        
        processor = FacialAIProcessor()
        config = ZoomConfig()
        
        # Create mock face detections
        face_detections = [
            FaceDetection(
                timestamp=1.0,
                bbox=(200, 150, 240, 220),
                confidence=0.9,
                landmarks=[]
            ),
            FaceDetection(
                timestamp=2.0,
                bbox=(210, 160, 250, 230),
                confidence=0.85,
                landmarks=[]
            )
        ]
        
        # Create mock voice activities
        voice_activities = [
            VoiceActivity(start_time=0.5, end_time=2.5, confidence=0.8)
        ]
        
        # Test correlation
        logger.info("Testing voice-face correlation...")
        speaker_segments = processor._correlate_voice_and_faces(voice_activities, face_detections)
        
        logger.info(f"✅ Correlation completed: {len(speaker_segments)} segments")
        
        # Test zoom timeline generation
        logger.info("Testing zoom timeline generation...")
        zoom_timeline = processor._generate_zoom_timeline(speaker_segments, config)
        
        logger.info(f"✅ Zoom timeline generated: {len(zoom_timeline)} entries")
        
        for i, entry in enumerate(zoom_timeline):
            logger.info(f"  - Entry {i+1}: {entry.get('start_time', 0):.2f}s - {entry.get('end_time', 0):.2f}s")
            logger.info(f"    zoom: {entry.get('zoom_level', 1.0)}x")
        
        if len(zoom_timeline) > 0:
            logger.info("🎉 SUCCESS: Zoom timeline generation is working!")
            return True
        else:
            logger.warning("⚠️  WARNING: No zoom timeline entries generated")
            return False
            
    except Exception as e:
        logger.error(f"❌ Zoom timeline test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_video_processing_mock():
    """Test video processing with mock zoom timeline"""
    logger.info("Testing video processing with mock data...")
    
    try:
        from facial_ai_processor import FacialAIProcessor, ZoomConfig
        
        processor = FacialAIProcessor()
        config = ZoomConfig()
        
        # Create test video
        input_path = create_test_video_with_face()
        output_path = tempfile.mktemp(suffix="_processed.mp4")
        
        # Create mock zoom timeline
        zoom_timeline = [
            {
                'start_time': 0.0,
                'end_time': 1.0,
                'zoom_level': 1.5,
                'target_bbox': (200, 150, 240, 220),
                'transition_speed': 0.5
            },
            {
                'start_time': 1.0,
                'end_time': 2.0,
                'zoom_level': 2.0,
                'target_bbox': (210, 160, 250, 230),
                'transition_speed': 0.3
            }
        ]
        
        logger.info("Testing video processing with zoom effects...")
        result_path = processor._apply_zoom_effects(input_path, output_path, zoom_timeline, config)
        
        logger.info(f"✅ Video processing completed")
        logger.info(f"  - Output: {result_path}")
        
        if os.path.exists(result_path):
            output_size = os.path.getsize(result_path)
            logger.info(f"  - Output size: {output_size} bytes")
            
            if output_size > 0:
                logger.info("🎉 SUCCESS: Video processing with zoom effects is working!")
                
                # Clean up
                os.remove(input_path)
                os.remove(result_path)
                return True
            else:
                logger.error("❌ Output video is empty")
                return False
        else:
            logger.error("❌ Output video not created")
            return False
            
    except Exception as e:
        logger.error(f"❌ Video processing test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function"""
    logger.info("🎬 Testing Facial AI Components Individually")
    logger.info("=" * 50)
    
    tests = [
        ("Face Detection", test_face_detection_directly),
        ("Zoom Timeline Generation", test_zoom_timeline_generation),
        ("Video Processing", test_video_processing_mock)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        logger.info(f"\n🧪 Running: {test_name}")
        logger.info("-" * 30)
        
        try:
            success = test_func()
            results.append((test_name, success))
            
            if success:
                logger.info(f"✅ {test_name} PASSED")
            else:
                logger.warning(f"⚠️  {test_name} FAILED")
                
        except Exception as e:
            logger.error(f"❌ {test_name} CRASHED: {e}")
            results.append((test_name, False))
    
    # Generate report
    logger.info("\n" + "=" * 50)
    logger.info("🎯 TEST RESULTS SUMMARY")
    logger.info("=" * 50)
    
    passed = sum(1 for _, success in results if success)
    total = len(results)
    
    logger.info(f"📊 Overall: {passed}/{total} tests passed")
    
    for test_name, success in results:
        status = "✅ PASS" if success else "❌ FAIL"
        logger.info(f"  - {test_name}: {status}")
    
    if passed == total:
        logger.info("\n🎉 ALL TESTS PASSED - Facial AI components are working!")
        logger.info("✅ Face detection is functional")
        logger.info("✅ Zoom timeline generation is functional") 
        logger.info("✅ Video processing with zoom effects is functional")
        return True
    else:
        logger.info(f"\n⚠️  {total - passed} tests failed - Some components need debugging")
        return False

if __name__ == "__main__":
    success = main()
    print(f"\n{'✅ COMPONENT TESTS PASSED' if success else '❌ COMPONENT TESTS FAILED'}")
    exit(0 if success else 1)
