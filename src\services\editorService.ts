import { ENV } from "@/lib/env";

const API_BASE_URL = ENV.VITE_API_URL || "http://localhost:8000";

// Types for editor API
export interface EditorProject {
  id?: string;
  name: string;
  description?: string;
  timeline_data: {
    duration: number;
    tracks: number;
  };
  clips: TimelineClip[];
  text_overlays: TextOverlay[];
  effects: Effect[];
  created_at?: string;
  updated_at?: string;
}

export interface TimelineClip {
  id: string;
  start: number;
  end: number;
  duration: number;
  url: string;
  title: string;
  track: number;
}

export interface TextOverlay {
  id: string;
  text: string;
  fontFamily: string;
  x: number;
  y: number;
  fontSize: number;
  color: string;
  startTime: number;
  endTime: number;
}

export interface Effect {
  id: string;
  type: string;
  parameters: Record<string, any>;
  startTime: number;
  endTime: number;
  target: string; // clip_id or "global"
}

export interface EffectTemplate {
  id: string;
  name: string;
  description: string;
  category: string;
  parameters: Record<string, any>;
}

export interface ExportOptions {
  format: string;
  quality: string;
  resolution: string;
  fps: number;
}

/**
 * Get authentication headers
 */
const getAuthHeaders = () => {
  const token = localStorage.getItem("auth_token");
  return {
    "Authorization": `Bearer ${token}`,
    "Content-Type": "application/json",
  };
};

/**
 * Create a new editor project
 */
export const createProject = async (
  project: EditorProject,
): Promise<EditorProject> => {
  try {
    const response = await fetch(`${API_BASE_URL}/editor/projects`, {
      method: "POST",
      headers: getAuthHeaders(),
      body: JSON.stringify(project),
    });

    if (!response.ok) {
      throw new Error(`Failed to create project: ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error("Error creating project:", error);
    throw error;
  }
};

/**
 * Get all editor projects
 */
export const getProjects = async (): Promise<EditorProject[]> => {
  try {
    const response = await fetch(`${API_BASE_URL}/editor/projects`, {
      headers: getAuthHeaders(),
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch projects: ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error("Error fetching projects:", error);
    throw error;
  }
};

/**
 * Get a specific editor project
 */
export const getProject = async (projectId: string): Promise<EditorProject> => {
  try {
    const response = await fetch(
      `${API_BASE_URL}/editor/projects/${projectId}`,
      {
        headers: getAuthHeaders(),
      },
    );

    if (!response.ok) {
      throw new Error(`Failed to fetch project: ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error("Error fetching project:", error);
    throw error;
  }
};

/**
 * Update an editor project
 */
export const updateProject = async (
  projectId: string,
  project: EditorProject,
): Promise<EditorProject> => {
  try {
    const response = await fetch(
      `${API_BASE_URL}/editor/projects/${projectId}`,
      {
        method: "PUT",
        headers: getAuthHeaders(),
        body: JSON.stringify(project),
      },
    );

    if (!response.ok) {
      throw new Error(`Failed to update project: ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error("Error updating project:", error);
    throw error;
  }
};

/**
 * Save project state
 */
export const saveProject = async (
  projectId: string,
  saveData: any,
): Promise<any> => {
  try {
    const response = await fetch(
      `${API_BASE_URL}/editor/projects/${projectId}/save`,
      {
        method: "POST",
        headers: getAuthHeaders(),
        body: JSON.stringify(saveData),
      },
    );

    if (!response.ok) {
      throw new Error(`Failed to save project: ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error("Error saving project:", error);
    throw error;
  }
};

/**
 * Delete an editor project
 */
export const deleteProject = async (projectId: string): Promise<void> => {
  try {
    const response = await fetch(
      `${API_BASE_URL}/editor/projects/${projectId}`,
      {
        method: "DELETE",
        headers: getAuthHeaders(),
      },
    );

    if (!response.ok) {
      throw new Error(`Failed to delete project: ${response.statusText}`);
    }
  } catch (error) {
    console.error("Error deleting project:", error);
    throw error;
  }
};

/**
 * Export project to video
 */
export const exportProject = async (
  projectId: string,
  options: ExportOptions,
): Promise<any> => {
  try {
    const response = await fetch(
      `${API_BASE_URL}/editor/projects/${projectId}/export`,
      {
        method: "POST",
        headers: getAuthHeaders(),
        body: JSON.stringify(options),
      },
    );

    if (!response.ok) {
      throw new Error(`Failed to export project: ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error("Error exporting project:", error);
    throw error;
  }
};

/**
 * Get export status
 */
export const getExportStatus = async (exportId: string): Promise<any> => {
  try {
    const response = await fetch(`${API_BASE_URL}/editor/exports/${exportId}`, {
      headers: getAuthHeaders(),
    });

    if (!response.ok) {
      throw new Error(`Failed to get export status: ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error("Error getting export status:", error);
    throw error;
  }
};

// --- Timeline Operations ---

/**
 * Add clip to timeline
 */
export const addClipToTimeline = async (
  projectId: string,
  clip: TimelineClip,
): Promise<any> => {
  try {
    const response = await fetch(
      `${API_BASE_URL}/editor/projects/${projectId}/clips`,
      {
        method: "POST",
        headers: getAuthHeaders(),
        body: JSON.stringify(clip),
      },
    );

    if (!response.ok) {
      throw new Error(`Failed to add clip: ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error("Error adding clip:", error);
    throw error;
  }
};

/**
 * Update timeline clip
 */
export const updateTimelineClip = async (
  projectId: string,
  clipId: string,
  updates: Partial<TimelineClip>,
): Promise<any> => {
  try {
    const response = await fetch(
      `${API_BASE_URL}/editor/projects/${projectId}/clips/${clipId}`,
      {
        method: "PUT",
        headers: getAuthHeaders(),
        body: JSON.stringify(updates),
      },
    );

    if (!response.ok) {
      throw new Error(`Failed to update clip: ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error("Error updating clip:", error);
    throw error;
  }
};

/**
 * Delete timeline clip
 */
export const deleteTimelineClip = async (
  projectId: string,
  clipId: string,
): Promise<any> => {
  try {
    const response = await fetch(
      `${API_BASE_URL}/editor/projects/${projectId}/clips/${clipId}`,
      {
        method: "DELETE",
        headers: getAuthHeaders(),
      },
    );

    if (!response.ok) {
      throw new Error(`Failed to delete clip: ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error("Error deleting clip:", error);
    throw error;
  }
};

/**
 * Split timeline clip
 */
export const splitTimelineClip = async (
  projectId: string,
  clipId: string,
  splitTime: number,
): Promise<any> => {
  try {
    const response = await fetch(
      `${API_BASE_URL}/editor/projects/${projectId}/clips/${clipId}/split`,
      {
        method: "POST",
        headers: getAuthHeaders(),
        body: JSON.stringify({ split_time: splitTime }),
      },
    );

    if (!response.ok) {
      throw new Error(`Failed to split clip: ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error("Error splitting clip:", error);
    throw error;
  }
};

/**
 * Duplicate timeline clip
 */
export const duplicateTimelineClip = async (
  projectId: string,
  clipId: string,
): Promise<any> => {
  try {
    const response = await fetch(
      `${API_BASE_URL}/editor/projects/${projectId}/clips/${clipId}/duplicate`,
      {
        method: "POST",
        headers: getAuthHeaders(),
      },
    );

    if (!response.ok) {
      throw new Error(`Failed to duplicate clip: ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error("Error duplicating clip:", error);
    throw error;
  }
};

// --- Text Overlays ---

/**
 * Add text overlay
 */
export const addTextOverlay = async (
  projectId: string,
  overlay: TextOverlay,
): Promise<any> => {
  try {
    const response = await fetch(
      `${API_BASE_URL}/editor/projects/${projectId}/text-overlays`,
      {
        method: "POST",
        headers: getAuthHeaders(),
        body: JSON.stringify(overlay),
      },
    );

    if (!response.ok) {
      throw new Error(`Failed to add text overlay: ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error("Error adding text overlay:", error);
    throw error;
  }
};

/**
 * Update text overlay
 */
export const updateTextOverlay = async (
  projectId: string,
  overlayId: string,
  updates: Partial<TextOverlay>,
): Promise<any> => {
  try {
    const response = await fetch(
      `${API_BASE_URL}/editor/projects/${projectId}/text-overlays/${overlayId}`,
      {
        method: "PUT",
        headers: getAuthHeaders(),
        body: JSON.stringify(updates),
      },
    );

    if (!response.ok) {
      throw new Error(`Failed to update text overlay: ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error("Error updating text overlay:", error);
    throw error;
  }
};

/**
 * Delete text overlay
 */
export const deleteTextOverlay = async (
  projectId: string,
  overlayId: string,
): Promise<any> => {
  try {
    const response = await fetch(
      `${API_BASE_URL}/editor/projects/${projectId}/text-overlays/${overlayId}`,
      {
        method: "DELETE",
        headers: getAuthHeaders(),
      },
    );

    if (!response.ok) {
      throw new Error(`Failed to delete text overlay: ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error("Error deleting text overlay:", error);
    throw error;
  }
};

// --- Effects ---

/**
 * Add effect
 */
export const addEffect = async (
  projectId: string,
  effect: Partial<Effect>,
): Promise<any> => {
  try {
    const response = await fetch(
      `${API_BASE_URL}/editor/projects/${projectId}/effects`,
      {
        method: "POST",
        headers: getAuthHeaders(),
        body: JSON.stringify(effect),
      },
    );

    if (!response.ok) {
      throw new Error(`Failed to add effect: ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error("Error adding effect:", error);
    throw error;
  }
};

/**
 * Get effect templates
 */
export const getEffectTemplates = async (): Promise<EffectTemplate[]> => {
  try {
    const response = await fetch(`${API_BASE_URL}/editor/effects/templates`, {
      headers: getAuthHeaders(),
    });

    if (!response.ok) {
      throw new Error(`Failed to get effect templates: ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error("Error getting effect templates:", error);
    throw error;
  }
};
