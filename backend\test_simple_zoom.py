"""
Simple test to verify facial AI zoom functionality step by step
"""

import os
import time
import tempfile
import cv2
import numpy as np
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_test_video():
    """Create a simple test video with a moving face-like object"""
    temp_file = tempfile.mktemp(suffix=".mp4")
    logger.info(f"Creating test video: {temp_file}")
    
    # Video parameters
    fps = 30
    width, height = 640, 480
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    out = cv2.VideoWriter(temp_file, fourcc, fps, (width, height))
    
    # Create 3 seconds of video
    for frame_num in range(90):
        # Create background
        frame = np.zeros((height, width, 3), dtype=np.uint8)
        
        # Add a moving "face" - simple rectangle that moves
        x = 200 + int(50 * np.sin(frame_num * 0.1))
        y = 200 + int(30 * np.cos(frame_num * 0.1))
        
        # Draw face rectangle
        cv2.rectangle(frame, (x-30, y-40), (x+30, y+40), (200, 180, 160), -1)
        
        # Add eyes
        cv2.circle(frame, (x-15, y-15), 5, (50, 50, 50), -1)
        cv2.circle(frame, (x+15, y-15), 5, (50, 50, 50), -1)
        
        # Add mouth
        cv2.rectangle(frame, (x-10, y+10), (x+10, y+20), (100, 50, 50), -1)
        
        out.write(frame)
    
    out.release()
    logger.info(f"Test video created: {os.path.getsize(temp_file)} bytes")
    return temp_file

def test_processor_import():
    """Test if we can import and create the processor"""
    logger.info("Testing processor import...")
    
    try:
        from facial_ai_processor import FacialAIProcessor, ZoomConfig
        logger.info("✅ Import successful")
        
        processor = FacialAIProcessor()
        logger.info("✅ Processor created successfully")
        
        config = ZoomConfig(zoom_level=1.5, detection_sensitivity=0.7)
        logger.info(f"✅ Config created: zoom={config.zoom_level}, sensitivity={config.detection_sensitivity}")
        
        return processor, config
        
    except Exception as e:
        logger.error(f"❌ Import/creation failed: {e}")
        raise

def test_video_analysis(processor, video_path, config):
    """Test video analysis functionality"""
    logger.info("Testing video analysis...")
    
    try:
        start_time = time.time()
        result = processor.analyze_video(video_path, config)
        analysis_time = time.time() - start_time
        
        logger.info(f"✅ Analysis completed in {analysis_time:.2f}s")
        logger.info(f"  - Result keys: {list(result.keys())}")
        logger.info(f"  - Face detections: {result.get('face_detections', 0)}")
        logger.info(f"  - Voice segments: {result.get('voice_segments', 0)}")
        logger.info(f"  - Speaker segments: {len(result.get('speaker_segments', []))}")
        logger.info(f"  - Zoom timeline: {len(result.get('zoom_timeline', []))}")
        
        return result
        
    except Exception as e:
        logger.error(f"❌ Analysis failed: {e}")
        import traceback
        traceback.print_exc()
        raise

def test_video_processing(processor, video_path, config):
    """Test video processing with zoom effects"""
    logger.info("Testing video processing...")
    
    try:
        output_path = tempfile.mktemp(suffix="_processed.mp4")
        
        start_time = time.time()
        result_path = processor.process_video_with_face_zoom(video_path, output_path, config)
        processing_time = time.time() - start_time
        
        logger.info(f"✅ Processing completed in {processing_time:.2f}s")
        logger.info(f"  - Output path: {result_path}")
        
        if os.path.exists(result_path):
            output_size = os.path.getsize(result_path)
            logger.info(f"  - Output size: {output_size} bytes")
            
            if output_size > 0:
                logger.info("✅ Output video created successfully")
                return result_path
            else:
                logger.error("❌ Output video is empty")
                return None
        else:
            logger.error("❌ Output video file not created")
            return None
            
    except Exception as e:
        logger.error(f"❌ Processing failed: {e}")
        import traceback
        traceback.print_exc()
        raise

def verify_zoom_effects(original_path, processed_path):
    """Verify that zoom effects were applied"""
    logger.info("Verifying zoom effects...")
    
    try:
        # Open both videos
        orig_cap = cv2.VideoCapture(original_path)
        proc_cap = cv2.VideoCapture(processed_path)
        
        if not orig_cap.isOpened() or not proc_cap.isOpened():
            logger.error("❌ Could not open video files")
            return False
        
        # Get frame counts
        orig_frames = int(orig_cap.get(cv2.CAP_PROP_FRAME_COUNT))
        proc_frames = int(proc_cap.get(cv2.CAP_PROP_FRAME_COUNT))
        
        logger.info(f"  - Original frames: {orig_frames}")
        logger.info(f"  - Processed frames: {proc_frames}")
        
        # Sample a few frames and compare
        sample_frames = [10, 30, 60]  # Sample at different points
        differences_found = 0
        
        for frame_num in sample_frames:
            if frame_num < min(orig_frames, proc_frames):
                orig_cap.set(cv2.CAP_PROP_POS_FRAMES, frame_num)
                proc_cap.set(cv2.CAP_PROP_POS_FRAMES, frame_num)
                
                ret1, orig_frame = orig_cap.read()
                ret2, proc_frame = proc_cap.read()
                
                if ret1 and ret2:
                    # Calculate difference
                    diff = cv2.absdiff(orig_frame, proc_frame)
                    mean_diff = np.mean(diff)
                    
                    logger.info(f"  - Frame {frame_num} difference: {mean_diff:.2f}")
                    
                    if mean_diff > 5:  # Threshold for detecting changes
                        differences_found += 1
        
        orig_cap.release()
        proc_cap.release()
        
        if differences_found > 0:
            logger.info(f"✅ Zoom effects detected ({differences_found}/{len(sample_frames)} frames show changes)")
            return True
        else:
            logger.warning("⚠️  No significant differences detected - zoom may not be working")
            return False
            
    except Exception as e:
        logger.error(f"❌ Verification failed: {e}")
        return False

def main():
    """Main test function"""
    logger.info("🎬 Starting Simple Facial AI Zoom Test")
    logger.info("=" * 50)
    
    video_path = None
    processed_path = None
    
    try:
        # Step 1: Test imports and processor creation
        processor, config = test_processor_import()
        
        # Step 2: Create test video
        video_path = create_test_video()
        
        # Step 3: Test video analysis
        analysis_result = test_video_analysis(processor, video_path, config)
        
        # Step 4: Test video processing
        processed_path = test_video_processing(processor, video_path, config)
        
        # Step 5: Verify zoom effects
        if processed_path:
            zoom_working = verify_zoom_effects(video_path, processed_path)
            
            if zoom_working:
                logger.info("🎉 SUCCESS: Facial AI zoom functionality is working!")
                return True
            else:
                logger.warning("⚠️  WARNING: Zoom effects may not be working correctly")
                return False
        else:
            logger.error("❌ FAILED: Could not process video")
            return False
            
    except Exception as e:
        logger.error(f"❌ Test failed with error: {e}")
        return False
        
    finally:
        # Clean up
        if video_path and os.path.exists(video_path):
            os.remove(video_path)
            logger.info(f"🧹 Cleaned up test video: {video_path}")
        
        if processed_path and os.path.exists(processed_path):
            os.remove(processed_path)
            logger.info(f"🧹 Cleaned up processed video: {processed_path}")

if __name__ == "__main__":
    success = main()
    print(f"\n{'✅ TEST PASSED' if success else '❌ TEST FAILED'}")
    exit(0 if success else 1)
